<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <!-- 继承 ks-boot-root-pom 而非 kuaishou-root-pom，勿改! -->
    <!-- 急需更新依赖1.0-SNAPSHOT；不关心二方依赖可使用1.0-WEEKLY-SNAPSHOT -->
    <parent>
        <groupId>com.kuaishou.infra.boot</groupId>
        <artifactId>ks-boot-root-pom</artifactId>
        <version>release-202506042045</version>
        <relativePath/>
    </parent>
    <groupId>kuaishou</groupId>
    <artifactId>kwaishop-qa-risk-center-parent</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description>${project.name}</description>
    <!-- FIXME git 地址请生成后自行填写 -->
    <scm>
        <connection>scm:git:</connection>
        <developerConnection>scm:git:</developerConnection>
        <url/>
        <tag>HEAD</tag>
    </scm>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou.infra.boot</groupId>
            <artifactId>ks-boot-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>krpc-all</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>krpc-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-mmu-serving-sdk</artifactId>
        </dependency>
    </dependencies>
    <!-- 开发人员说明 -->
    <developers>
        <developer>
            <name>chenshengrui</name>
        </developer>
    </developers>
    <properties>
        <!-- 当前工程版本号 -->
        <revision>1.0.0-SNAPSHOT</revision>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>kuaishou</groupId>
                <artifactId>kwaishop-qa-risk-center-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>kuaishou</groupId>
                <artifactId>kwaishop-qa-risk-center-client</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>kuaishou</groupId>
                <artifactId>kwaishop-sellerdata-management-service-client</artifactId>
                <version>1.0.58</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.30</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>kuaishou</groupId>
                <artifactId>kwaishop-qa-risk-center-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>ru.yandex.clickhouse</groupId>
                <artifactId>clickhouse-jdbc</artifactId>
                <version>1.0.4-kwai-0.2.3</version>
            </dependency>
            <dependency>
                <groupId>kuaishou</groupId>
                <artifactId>kwaishop-apollo-strategy-center-client</artifactId>
                <version>1.0.73</version>
            </dependency>
            <dependency>
                <groupId>kuaishou</groupId>
                <artifactId>kwaishop-biz-account-center-client</artifactId>
                <version>1.0.15</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-core</artifactId>
                <version>1.36.1-kwai-1.1</version>
            </dependency>
            <dependency>
                <groupId>kuaishou</groupId>
                <artifactId>kwaishop-lang-bridge-sdk</artifactId>
                <version>1.0.17-feature_20250529_images-BETA-SNAPSHOT</version>
            </dependency>
            <!-- Spring AI -->
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-openai-spring-boot-starter</artifactId>
                <version>0.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-anthropic-spring-boot-starter</artifactId>
                <version>0.8.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 用于替换 application.yml 中的变量 -->
                <mvn.profile.active>dev</mvn.profile.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <modules>
                <module>kwaishop-qa-risk-center-starter</module>
                <module>kwaishop-qa-risk-center</module>
                <module>kwaishop-qa-risk-center-common</module>
                <module>kwaishop-qa-risk-center-client</module>
            </modules>
        </profile>
        <profile>
            <id>test</id>
            <modules>
                <module>kwaishop-qa-risk-center-starter</module>
                <module>kwaishop-qa-risk-center</module>
                <module>kwaishop-qa-risk-center-common</module>
                <module>kwaishop-qa-risk-center-client</module>
            </modules>
            <properties>
                <!-- 用于替换 application.yml 中的变量 -->
                <mvn.profile.active>test</mvn.profile.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <modules>
                <module>kwaishop-qa-risk-center-starter</module>
                <module>kwaishop-qa-risk-center</module>
                <module>kwaishop-qa-risk-center-common</module>
                <module>kwaishop-qa-risk-center-client</module>
            </modules>
            <properties>
                <!-- 用于替换 application.yml 中的变量 -->
                <mvn.profile.active>prod</mvn.profile.active>
            </properties>
        </profile>
        <profile>
            <id>release-sdk</id>
            <modules>
                <module>kwaishop-qa-risk-center-client</module>
            </modules>
        </profile>
    </profiles>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <!-- checkstyle beta版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.2.0-kwai17-1</version>
            </plugin>
        </plugins>
    </build>
</project>