package com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert;

import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityPageDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityPBDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityPBPageDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
public abstract class EntityConvertAbstract implements EntityConvertHandler {

    @Override
    public EntityQueryBO buildEntityQueryBO(Integer entityType, Long entityId) {
        return EntityQueryBO.builder()
                .entityType(entityType)
                .entityId(entityId)
                .build();
    }

    @Override
    public EntityDTO buildDOToDTO(EntityDO entityDO) {
        return EntityDTO.builder()
                .id(entityDO.getId())
                .entityType(entityDO.getEntityType())
                .entityId(entityDO.getEntityId())
                .name(entityDO.getName())
                .status(entityDO.getStatus())
                .creator(entityDO.getCreator())
                .modifier(entityDO.getModifier())
                .createTime(entityDO.getCreateTime())
                .updateTime(entityDO.getUpdateTime())
                .ext(entityDO.getExt())
                .extra1(entityDO.getExtra1())
                .extra2(entityDO.getExtra2())
                .extra3(entityDO.getExtra3())
                .uic(entityDO.getUic())
                .build();
    }

    @Override
    public EntityPBDTO buildPBDTO(EntityDTO entityDTO) {
        EntityPBDTO.Builder builder = EntityPBDTO.newBuilder();
        if (StringUtils.isNotBlank(entityDTO.getExt())) {
            builder.setExt(entityDTO.getExt());
        }
        builder.setId(entityDTO.getId());
        builder.setEntityId(entityDTO.getEntityId());
        builder.setEntityType(entityDTO.getEntityType());
        builder.setName(entityDTO.getName());
        builder.setStatus(entityDTO.getStatus());
        builder.setExtra1(entityDTO.getExtra1());
        builder.setExtra2(entityDTO.getExtra2());
        builder.setExtra3(entityDTO.getExtra3());
        builder.setCreator(entityDTO.getCreator());
        builder.setModifier(entityDTO.getModifier());
        builder.setCreateTime(entityDTO.getCreateTime());
        builder.setUpdateTime(entityDTO.getUpdateTime());
        return builder.build();
    }

    @Override
    public EntityPBPageDTO buildPBPageDTO(EntityPageDTO entityPageDTO) {
        return EntityPBPageDTO.newBuilder()
                .setPageNo(entityPageDTO.getPageNo())
                .setPageSize(entityPageDTO.getPageSize())
                .setTotal(entityPageDTO.getTotal())
                .addAllEntityInfos(entityPageDTO.getData().stream()
                        .map(this::buildPBDTO).collect(Collectors.toList()))
                .build();
    }
}
