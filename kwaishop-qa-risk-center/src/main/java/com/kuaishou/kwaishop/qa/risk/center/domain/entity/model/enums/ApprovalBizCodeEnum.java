package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/8 17:43
 * @注释 业务权限申请 bizcode枚举
 */
public enum ApprovalBizCodeEnum {
    UN_KNOW("0000", "未知"),

    ACCOUNT("1000", "新版账号租借"),

    ACCOUNT_TAG_ADD("1001", "打标"),

    ACCOUNT_TAG_DELETE("1002", "下标"),

    TAG_ADD("1003", "添加标签"),

    ACCOUNT_ADD("1004", "新版添加账号"),

    OLD_ACCOUNT("2000", "老的账号池租借");

    private final String code;

    private final String desc;

    ApprovalBizCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ApprovalBizCodeEnum fromBizCode(String code) {
        for (ApprovalBizCodeEnum value : ApprovalBizCodeEnum.values()) {
            if (StringUtils.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
