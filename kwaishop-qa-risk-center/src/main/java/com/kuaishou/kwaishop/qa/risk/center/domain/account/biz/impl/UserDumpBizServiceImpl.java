package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.impl;


import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserDbForDumpDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.UserDumpBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.DatafactoryService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserDumpService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetEsDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetEsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.KafkaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryEsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAccountDumpRequest;

@Service
public class UserDumpBizServiceImpl implements UserDumpBizService {

    @Resource
    private UserDumpService userDumpService;

    @Resource
    private DatafactoryService datafactoryService;


    @Override
    public void insertDump(UserAccountDumpRequest userAccountDumpRequest) {

        UserDbForDumpDO userDbForDumpDO = UserDbForDumpDO.builder().userId(userAccountDumpRequest.getUserId())
                .ext(ObjectMapperUtils.toJSON(userAccountDumpRequest.getExt())).build();
        userDumpService.insertToDump(userDbForDumpDO);

    }


    @Override
    public UserDbForDumpDO getDumpUser(GetTestAccountRequest userAccountDumpRequest) {
        return userDumpService.getDumpUser(Long.valueOf(userAccountDumpRequest.getScene()));
    }

    @Override
    public QueryEsResponse getFromEs(GetEsRequest getEsRequest) {
        String data = datafactoryService.getFromEs(getEsRequest.getColony(), getEsRequest.getName(), getEsRequest.getId());
        QueryEsResponse queryEsResponse = QueryEsResponse.newBuilder().setData(data).build();
        return queryEsResponse;
    }


    @Override
    public QueryEsResponse getFromEsWithData(GetEsDataRequest getEsRequest) {
        String data = datafactoryService.getFromEsWithData(getEsRequest.getColony(),
                getEsRequest.getName(), getEsRequest.getKeys(), getEsRequest.getValues());
        QueryEsResponse queryEsResponse = QueryEsResponse.newBuilder().setData(data).build();
        return queryEsResponse;
    }

    @Override
    public void sendKafkaMsg(KafkaRequest request) {

        userDumpService.sendKafkaMsg(request.getTopic(), request.getMsg());

    }


}
