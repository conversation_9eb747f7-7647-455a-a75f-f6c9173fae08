package com.kuaishou.kwaishop.qa.risk.center.db.mapper.feature;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.bo.KatCaseRecordBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.feature.KspayRiskFeatureDetail;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface FundsRiskFeatureBranchMapper extends BaseMapper<FundsRiskFeatureBranchDO> {
    @Update("update funds_risk_feature_view_branch set updater = #{updater}, update_time = #{updateTime}, "
            + "risk_status = #{riskStatus}, audit_cover = #{auditCover}, risk_status_updater = #{riskStatusUpdater}, "
            + "audit_cover_updater = #{auditCoverUpdater} "
            + "where feature_view_id = #{featureViewId}")
    int updateFeatureViewBranchByFeatureViewId(@Param("updater") String updater, @Param("updateTime") Long updateTime,
                                               @Param("riskStatus") Integer riskStatus, @Param("auditCover") Integer auditCover,
                                               @Param("riskStatusUpdater") String riskStatusUpdater,
                                               @Param("auditCoverUpdater") String auditCoverUpdater,
                                               @Param("featureViewId") Integer featureViewId);

    @Update("update funds_risk_feature_view_branch set updater = #{updater}, update_time = #{updateTime}, risk_status = #{riskStatus}, "
            + "risk_status_updater = #{riskStatusUpdater}, audit_cover_updater = #{auditCoverUpdater} "
            + "where id = #{id}")
    int updateBranchRiskStatusById(@Param("updater") String updater, @Param("updateTime") Long updateTime,
                                   @Param("riskStatus") Integer riskStatus, @Param("riskStatusUpdater") String riskStatusUpdater,
                                   @Param("auditCoverUpdater") String auditCoverUpdater, @Param("id") String id);

    @Update("update funds_risk_feature_view_branch set updater = #{updater}, update_time = #{updateTime}, audit_cover = #{auditCover}, "
            + "risk_status_updater = #{riskStatusUpdater}, audit_cover_updater = #{auditCoverUpdater} "
            + "where id = #{id}")
    int updateBranchAuditCoverById(@Param("updater") String updater, @Param("updateTime") Long updateTime,
                                   @Param("auditCover") Integer auditCover, @Param("riskStatusUpdater") String riskStatusUpdater,
                                   @Param("auditCoverUpdater") String auditCoverUpdater, @Param("id") String id);

    @Update("update funds_risk_feature_view_branch set updater = #{updater}, update_time = #{updateTime}, risk_status = #{riskStatus}, "
            + "audit_cover = #{auditCover}, risk_status_updater = #{riskStatusUpdater}, audit_cover_updater = #{auditCoverUpdater} "
            + "where id = #{id}")
    int updateAllStatusByMainId(@Param("updater") String updater,
                                @Param("updateTime") Long updateTime,
                                @Param("riskStatus") Integer riskStatus,
                                @Param("auditCover") Integer auditCover,
                                @Param("riskStatusUpdater") String riskStatusUpdater,
                                @Param("auditCoverUpdater") String auditCoverUpdater,
                                @Param("id") String id);

    @Update("update funds_risk_feature_view_branch set updater = #{updater}, update_time = #{updateTime}, risk_status = #{riskStatus}, "
            + "audit_cover = #{auditCover}, risk_status_updater = #{riskStatusUpdater}, audit_cover_updater = #{auditCoverUpdater} "
            + "where feature_view_id = #{featureViewId}")
    int updateAllStatusByFeatureId(@Param("updater") String updater,
                                   @Param("updateTime") Long updateTime,
                                   @Param("riskStatus") Integer riskStatus,
                                   @Param("auditCover") Integer auditCover,
                                   @Param("riskStatusUpdater") String riskStatusUpdater,
                                   @Param("auditCoverUpdater") String auditCoverUpdater,
                                   @Param("featureViewId") Integer featureViewId);

    @Update("update funds_risk_feature_view_branch set data = #{extraData} where id = #{id}")
    void updateExtraDataByFeatureId(@Param("id") Long id, @Param("extraData") String extraData);

    @Update("update funds_risk_feature_view_branch set rule_is_valid = #{ruleIsValid} where repo_name = #{repoName} "
            + "and branch_name = #{branchName} and feature_view_id = #{featureViewId}")
    int updateRuleIsValidByMixed(@Param("repoName") String repoName,
                                 @Param("branchName") String branchName,
                                 @Param("featureViewId") Long featureViewId,
                                 @Param("ruleIsValid") String ruleIsValid);

    @Select("select * from funds_risk_feature_view_branch where repo_name = #{repoName} and branch_name = #{branchName}")
    List<FundsRiskFeatureBranchDO> queryFundRiskByRepoAndBranch(@Param("repoName") String repoName, @Param("branchName") String branchName);

    @Select("SELECT * FROM funds_risk_feature_view_branch WHERE risk_table_fields IS NOT NULL")
    List<KspayRiskFeatureDetail> queryRiskBranchStatistic();

    @Select("select * from funds_risk_feature_view_branch where repo_name = #{repoName} and branch_name = #{branchName} "
            + "and feature_view_id = #{featureViewId} ")
    FundsRiskFeatureBranchDO queryFundRiskByRepoAndBranchAndViewId(@Param("repoName") String repoName,
                                                                   @Param("branchName") String branchName,
                                                                   @Param("featureViewId") Long featureViewId);

    @Select("select * from funds_risk_feature_view_branch where data != ''")
    List<FundsRiskFeatureBranchDO> queryFundRiskFeatureBranchDataNoNull();

    @Select("SELECT * from funds_risk_feature_view_branch WHERE fund_risk_method_amount != 0")
    List<FundsRiskFeatureBranchDO> queryFundRiskFeatureBranchDataNoRisk();

    @Select("SELECT * FROM funds_risk_feature_view_branch WHERE method_entrance IS NOT NULL OR risk_table_fields IS NOT NULL")
    List<FundsRiskFeatureBranchDO> getKspayRiskStatistic1();

    @Select("SELECT * FROM funds_risk_feature_view_branch "
            + "WHERE fund_risk_method_amount > 0 AND method_entrance IS NOT NULL "
            + "ORDER BY create_time DESC LIMIT 20000")
    List<FundsRiskFeatureBranchDO> getKspayRiskStatistic2();

    @Select("SELECT * FROM funds_risk_feature_view_branch "
            + "WHERE fund_risk_method_amount > 0 "
            + "AND (method_entrance IS NOT NULL OR (risk_table_fields IS NOT NULL AND JSON_EXTRACT(risk_table_fields, '$') != '[]')) "
            + "ORDER BY create_time DESC LIMIT 10000")
    List<FundsRiskFeatureBranchDO> getKspayRiskStatistic();

    @Select("SELECT * FROM funds_risk_feature_view_branch "
            + "WHERE fund_risk_method_amount > 0 "
            + "AND (method_entrance IS NOT NULL OR (risk_table_fields IS NOT NULL AND JSON_EXTRACT(risk_table_fields, '$') != '[]')) "
            + "AND create_time BETWEEN #{startTime} AND #{endTime} "
            + "ORDER BY create_time DESC LIMIT 10000")
    List<FundsRiskFeatureBranchDO> getKspayRiskStatisticWithTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    @Select("SELECT rule_is_valid FROM funds_risk_feature_view_branch WHERE repo_name = #{repoName} AND "
            + "branch_name = #{branchName} AND feature_view_id = #{featureViewId}")
    String queryRuleIsValid(@Param("repoName") String repoName,
                                           @Param("branchName") String branchName,
                                           @Param("featureViewId") Long featureViewId);

    @Update("<script>"
            + "<foreach item='item' index='index' collection='list' separator=';'> "
            + "UPDATE funds_risk_feature_view_branch "
            + "SET `kat_record` = #{item.katRecord} "
            + "WHERE `branch_name` = #{branchName} "
            + "AND `repo_name` = #{repoName} "
            + "</foreach> "
            + "</script>")
    void updateKatCaseRecord(@Param("list") List<KatCaseRecordBO> records,
                             @Param("branchName") String branchName,
                             @Param("repoName") String repoName);
}
