package com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressServiceRecordBO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StressServiceRecordMapper extends BaseMapper<StressServiceRecordDO> {
    @Select({"<script>"
            + "SELECT * FROM stress_service_record "
            + " WHERE "
            + "id = #{id}"
            + "</script>"})
    StressServiceRecordDO queryStressServiceRecordById(@Param("id") Long id);

    @Select({"<script>"
            + "SELECT * FROM stress_service_record "
            + " WHERE 1=1"
            + "<if test='scenarioRecordId > 0'> and scenario_record_id = #{scenarioRecordId}</if>"
            + "</script>"})
    List<StressServiceRecordDO> queryStressServiceRecords(QueryStressServiceRecordBO  queryStressServiceRecordBO);
}
