package com.kuaishou.kwaishop.qa.risk.center.domain.entity.service;

import static com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException.throwBizException;
import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.DELETE_ENTITY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_ID_NOT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_NAME_NOT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_TYPE_NOT_FOUND_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.INSERT_ENTITY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.UPDATE_ENTITY_ERROR;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.EntityDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.EntityExtendDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertFactory;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityPageDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-09
 */
@Slf4j
public abstract class EntityAbstract implements EntityService {

    @Autowired
    private EntityDAO entityDAO;

    @Autowired
    private EntityExtendDAO entityExtendDAO;

    @Autowired
    private EntityConvertFactory entityConvertFactory;

    public abstract Integer getEntityType();


    protected EntityDO insert(EntityDO entityDO) {
        if (entityDO == null) {
            throwBizException(INSERT_ENTITY_ERROR);
        }
        if (StringUtils.isBlank(entityDO.getName())) {
            throwBizException(ENTITY_NAME_NOT_EMPTY_ERROR);
        }
        long res = entityDAO.insert(entityDO);
        if (res <= 0) {
            throwBizException(INSERT_ENTITY_ERROR);
        }
        return entityDO;
    }

    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    protected boolean batchInsert(List<EntityDO> entityDOS) {
        if (CollectionUtils.isEmpty(entityDOS)) {
            throwBizException(INSERT_ENTITY_ERROR);
        }
        entityDOS.stream()
                .filter(e -> e.getEntityType() == null || EntityTypeEnum.of(e.getEntityType()) == null)
                .findFirst()
                .ifPresent(typeError -> throwBizException(ENTITY_TYPE_NOT_FOUND_ERROR));
        entityDOS.stream()
                .filter(e -> StringUtils.isBlank(e.getName()))
                .findFirst()
                .ifPresent(nameError -> throwBizException(ENTITY_NAME_NOT_EMPTY_ERROR));
        boolean res = entityExtendDAO.saveBatch(entityDOS);
        if (!res) {
            throwBizException(INSERT_ENTITY_ERROR);
        }
        return true;
    }

    protected void update(EntityDO entityDO) {
        if (entityDO == null) {
            throwBizException(UPDATE_ENTITY_ERROR);
        }
        if (entityDO.getId() == null) {
            throwBizException(ENTITY_ID_NOT_EMPTY_ERROR);
        }
        if (StringUtils.isBlank(entityDO.getName())) {
            throwBizException(ENTITY_NAME_NOT_EMPTY_ERROR);
        }
        int res = entityDAO.updateSelectiveById(entityDO);
        if (res <= 0) {
            throwBizException(UPDATE_ENTITY_ERROR);
        }
    }

    protected abstract void deleteCheck(EntityDO existDO, String operator);

    @Override
    public void logicDelete(String operator, Long id) {
        EntityDO existDO = entityDAO.queryById(id);
        if (existDO == null) {
            throwBizException(ENTITY_ID_NOT_EMPTY_ERROR);
        }
        deleteCheck(existDO, operator);
        int res = entityDAO.logicDelete(operator, id);
        if (res <= 0) {
            throwBizException(DELETE_ENTITY_ERROR);
        }
    }

    @Override
    public void logicDelete(String operator, Integer entityType, Long entityId) {
        EntityConvertHandler handler = entityConvertFactory.getHandler(getEntityType());
        EntityQueryBO entityQueryBO = handler.buildEntityQueryBO(entityType, entityId);
        List<EntityDO> entityDOS = entityDAO.queryEntityList(entityQueryBO);
        if (CollectionUtils.isEmpty(entityDOS)) {
            throwBizException(ENTITY_TYPE_NOT_FOUND_ERROR);
        }
        int res = entityDAO.logicDelete(operator, entityType, entityId);
        if (res <= 0) {
            throwBizException(DELETE_ENTITY_ERROR);
        }
    }

    @Override
    public List<EntityDTO> queryEntityList(EntityQueryBO queryBO) {
        if (queryBO.getEntityType() == null || EntityTypeEnum.of(queryBO.getEntityType()) == null) {
            throwBizException(ENTITY_TYPE_NOT_FOUND_ERROR);
        }
        EntityConvertHandler handler = entityConvertFactory.getHandler(getEntityType());
        List<EntityDO> entityDOList = entityDAO.queryEntityList(queryBO);
        if (CollectionUtils.isNotEmpty(entityDOList)) {
            return entityDOList.stream().map(handler::buildDOToDTO).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public EntityPageDTO queryEntityPageList(EntityQueryBO queryBO) {
        if (queryBO.getEntityType() == null || EntityTypeEnum.of(queryBO.getEntityType()) == null) {
            throwBizException(ENTITY_TYPE_NOT_FOUND_ERROR);
        }
        EntityConvertHandler handler = entityConvertFactory.getHandler(getEntityType());
        PageBO<EntityDO> pageBO = entityDAO.queryEntityPageList(queryBO);
        List<EntityDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageBO.getData())) {
            dtoList = pageBO.getData().stream().map(handler::buildDOToDTO).collect(Collectors.toList());
        }
        return EntityPageDTO.builder()
                .total(pageBO.getTotal())
                .pageNo(pageBO.getPageNo())
                .pageSize(pageBO.getPageSize())
                .data(dtoList)
                .build();
    }

    @Override
    public List<EntityDO> queryByEntityType() {
        return entityDAO.queryByEntityType(getEntityType());
    }

    @Override
    public Map<Long, EntityDO> queryGroupByEntityType() {
        return entityDAO.queryGroupByEntityType(getEntityType());
    }

    @Override
    public List<EntityDO> queryByEntityId(Long entityId) {
        return entityDAO.queryByEntityId(getEntityType(), entityId);
    }

    @Override
    public List<EntityDO> queryByEntityIds(Collection<Long> entityIds) {
        return entityDAO.queryByEntityIds(getEntityType(), entityIds);
    }

    @Override
    public Map<Long, List<EntityDO>> queryGroupByEntityId(Collection<Long> entityIds) {
        return entityDAO.queryGroupByEntityId(getEntityType(), entityIds);
    }
}
