package com.kuaishou.kwaishop.qa.risk.center.db.mapper.account;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserDbForDumpDO;




@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface UserDumpMapper extends BaseMapper<UserDbForDumpDO> {


    @Insert("INSERT INTO user_db_for_dump "
            + "(id, center_id, team_id, user_id, login_type, account, account_type, data_type, ext, "
            + "status, create_time, update_time, creator, modifier, deleted, version) "
            + "VALUES(#{id}, 1,1, #{userId},1, 1, "
            + "1, 1, #{ext}, 1, 1, 1, "
            + "1, 1, 1, 1)")
    int insert(UserDbForDumpDO userDbForDumpDO);


    @Select("select * from user_db_for_dump where user_id = #{user_id}")
    UserDbForDumpDO getDumpUser(@Param("user_id") Long userId);


}
