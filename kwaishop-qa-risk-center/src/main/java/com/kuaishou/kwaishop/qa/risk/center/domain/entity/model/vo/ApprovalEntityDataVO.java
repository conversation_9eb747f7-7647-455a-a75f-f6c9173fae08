package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.vo;

import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.DPMApprovalParam;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/8 17:52
 * @注释 流程启动参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApprovalEntityDataVO {
    private String businessSummary;
    private String businessExplain;
    private String businessId;
    private String formType;
    private String processKey;
    private String processDefinitionKey;
    private String restartProcessWithVersion;
    private DPMApprovalParam processVariables;
    private String username;
    private String userId;
    private String duration;
}
