package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo;

import java.util.Collection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class UserAccountBO {

    private Long id;

    /**
     * 质量中心id
     */
    private Long centerId;

    /**
     * 团队id
     */
    private Long teamId;

    /**
     * 账号
     */
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 用户id
     */
    private Long userId;

    private Long bId;

    /**
     * 登录类型，1- 86手机号登录，2- 1264手机号登录，3- 邮箱登录
     */
    private Integer loginType;

    /**
     * 账号类型，1-平台导入账号，2-个人导入账号
     */
    private Integer accountType;

    /**
     * 数据类型，1-全数据类型，2-单uid数据类型
     */
    private Integer dataType;

    private String ext;

    /**
     * 账号状态，1-可借用，2-不可借用 3-自动化账号（不可借用）
     */
    private Integer status;

    private Collection<Long> userIds;

    private Collection<Long> ids;

    private Collection<Integer> loginTypes;

    private Collection<Integer> accountTypes;

    private Collection<Integer> dataTypes;

    private Collection<Integer> statuses;

    private String operator;

    private Integer pageNo;

    private Integer pageSize;

    private Integer merchantPermission;

    private Integer distributorPermission;

    private Integer celebrityPermission; //达人分下权限

    private Integer recruitingLeader;
}
