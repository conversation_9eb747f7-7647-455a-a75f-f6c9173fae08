package com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityPageDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.CreateEntityRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityPBDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityPBPageDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.UpdateEntityRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
public interface EntityConvertHandler {

    EntityQueryBO buildEntityQueryBO(Integer entityType, Long entityId);

    EntityDO buildCreateDO(EntityBO entityBO);

    EntityDTO buildDOToDTO(EntityDO entityDO);

    EntityDO buildModifyDO(EntityDO existDO, EntityBO entityBO);

    Integer getEntityType();

    EntityBO buildCreateBO(CreateEntityRequest request);

    EntityBO buildModifyBO(UpdateEntityRequest request);

    EntityQueryBO buildQueryListBO(QueryEntityListRequest request);

    EntityQueryBO buildQueryPageListBO(QueryEntityPageListRequest request);

    EntityPBDTO buildPBDTO(EntityDTO entityDTO);

    EntityPBPageDTO buildPBPageDTO(EntityPageDTO entityPageDTO);
}
