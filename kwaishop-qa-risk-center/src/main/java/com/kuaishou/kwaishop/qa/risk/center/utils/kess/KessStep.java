package com.kuaishou.kwaishop.qa.risk.center.utils.kess;


import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.jayway.jsonpath.JsonPath;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.KessHttpUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.dto.KessDto;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.dto.KessRun;

import lombok.extern.slf4j.Slf4j;


@Slf4j
public final class KessStep {

    private static String url = KessUrlEnum.STAGING.getUrl();

    private static final String PRT_TEST = "PRT.test";

    static {
        String laneId = System.getenv("env_test");
        log.info("laneId = {}", laneId);
        url = PRT_TEST.equals(laneId) ? KessUrlEnum.ONLINE.getUrl() : KessUrlEnum.STAGING.getUrl();
    }

    public static List<String> selectGrpc(KessRun kessRun) {
        String api = "/scheduler/api/searchServiceTree";
        String apiUrl = url + api;
        log.info("psp:{}", ObjectMapperUtils.toJSON(kessRun));
        Map<String, String> header = new HashMap<>();
        header.put("accept", "application/json, text/plain, */*");
        header.put("Content-Type", "text/json");
        header.put("Cookie", kessRun.getKessSsoCookie());
        String res = KessHttpUtils.postJson(apiUrl, kessRun.getRpcName(), header);
        log.info("res:{}", res);
        if (res == null) {
            return new ArrayList<>();
        }
        List<String> rpcList = JsonPath.read(res, "$..[?(@.type=='KESS')].name");
        return rpcList;


    }

    public static KessDto selectHost(String rpcName, String cookie, String lane) {
        String api = "/scheduler/api/readInstanceRouteTable";
        String apiUrl = url + api;

        Map<String, String> header = new HashMap<>();
        header.put("Cookie", cookie);


        Map<String, Object> para = new HashMap<>();
        para.put("cluster", "PRODUCTION");
        para.put("service", rpcName);

        String res = KessHttpUtils.postJson(apiUrl, para.toString(), header);


        Map<String, Object> result = (Map<String, Object>) ObjectMapperUtils.fromJson(res).get("result");

        Map<String, Object> rp = (Map<String, Object>) result.get("original");

        Set<String> strings = rp.keySet();

        String host = "";
        Integer port = 0;


        for (String key : strings) {
            Map<String, Object> value = (Map<String, Object>) rp.get(key);
            Set<String> strings1 = value.keySet();

            for (String lp : strings1) {
                if (lp.equals("lane") && value.get(lp).equals(lane)) {
                    host = (String) value.get("host");
                    Map<String, Object> entries = (Map<String, Object>) value.get("entries");
                    port = (Integer) entries.get("grpc");
                }

            }
        }


        KessDto kessDto = KessDto.builder().host(host).port(port).build();

        return kessDto != null ? kessDto : null;


    }


    public static String selectFunName(String kessName, KessDto hostPort, String cookie) {

        String api = "/debugger/v1/listMethods";
        String urlLine = url + api;


        Map<String, Object> host = new HashMap<>();

        host.put(hostPort.getHost(), hostPort.getPort());


        Map<String, Object> data = new HashMap<>();

        data.put("kessName", kessName);
        data.put("hostinfo", host.toString());
        data.put("Cookie", cookie);


        String res = KessHttpUtils.get(urlLine, data);


        return res;

    }


    public static String runKessCurl(String urlService, String urlMethod, KessDto host, String jsonData, String landId) throws IOException {


        String urlCall = url + "/debugger/v1/call?";


        Map<String, Object> map = new HashMap<>();
        map.put("kessName", urlService);
        map.put("methodName", urlMethod);
        map.put("request", jsonData);
        map.put("laneId", landId == null ? "" : landId);
        map.put("hostInfo", host.getHost() + ":" + host.getPort());


        Map<String, String> header = new HashMap<>();

        header.put("Cookie", "KSPP=P8TrnnursQc/VH8sBFv2b0+vrNjtlzm+4iGDGyIOA3ETe7Uxot45IQ==");
        header.put("Accept", "application/json, text/plain, */*");
        header.put("Content-Type", "application/json;charset=UTF-8");

        String res = KessHttpUtils.postJson(urlCall, ObjectMapperUtils.toPrettyJson(map), header);

        Map s = ObjectMapperUtils.fromJson(res);
        String r = (String) s.get("response");

        return r.trim().replaceAll("\n", "").replaceAll(" ", "");


    }


}