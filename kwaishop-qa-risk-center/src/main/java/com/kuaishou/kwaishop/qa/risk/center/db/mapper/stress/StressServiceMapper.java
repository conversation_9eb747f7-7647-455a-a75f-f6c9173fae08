package com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StressServiceMapper extends BaseMapper<StressServiceDO> {

    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    @Insert("insert into stress_service(service_name,service_desc,status,deleted,create_time,update_time)"
            + " values(#{serviceName},#{serviceDesc},#{status},#{deleted},now(),now())")
    Integer insertService(StressServiceDO stressServiceDO);


    @Select({"<script>"
            + "SELECT * FROM stress_service Where service_name = #{serviceName}"
            + " ORDER BY create_time DESC LIMIT 0,1 "
            + "</script>"})
    StressServiceDO selectByServiceName(@Param("serviceName") String serviceName);

}
