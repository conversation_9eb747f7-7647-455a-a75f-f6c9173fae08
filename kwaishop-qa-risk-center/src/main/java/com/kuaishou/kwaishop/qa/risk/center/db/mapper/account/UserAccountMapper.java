package com.kuaishou.kwaishop.qa.risk.center.db.mapper.account;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-28
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface UserAccountMapper extends BaseMapper<UserAccountDO> {

    /**
     * 改租借状态，TODO：考虑需不需要更新操作人
     *
     * @param status
     * @param userId
     * @return
     */
    @Update("update user_account set status = #{status} where user_id = #{user_id}")
    int updateRentStatus(@Param("status") Integer status, @Param("user_id") Long userId);

    @Select("select * from user_account where user_id = #{user_id}")
    List<UserAccountDO> queryUserAccountByUserId(@Param("user_id") Long userId);

    @Update("update user_account set ext = #{ext} where user_id = #{user_id}")
    void updateToken(@Param("user_id") Long userId, @Param("ext") String ext);

    @Update("update user_account set rent_num = rent_num + #{increment} where user_id = #{user_id}")
    int addRentNum(@Param("increment") Integer increment, @Param("user_id") Long userId);

    @Update("update user_account set rent_num = rent_num - #{increment} where user_id = #{user_id}")
    int reduceRentNum(@Param("increment") Integer increment, @Param("user_id") Long userId);

    @Update("update user_account set team_id = #{team_id}")
    int updateTimeId(@Param("team_id") Integer teamId);

    @Select("SELECT * FROM user_account WHERE CAST(SUBSTRING(user_id, 1, 3) AS UNSIGNED) BETWEEN #{from} AND #{to}")
    List<UserAccountDO> queryBusinessAccountByRule(@Param("from") Integer from, @Param("to") Integer to);

    @Update("update user_account set account = #{account},password = #{password} where id = #{id}")
    void fixAccount(@Param("id") int id, @Param("account") String account, @Param("password") String password);

    @Select("select id,user_id,creator,modifier from user_account")
    List<UserAccountDO> selectAllAccountsWithCreator();
}
