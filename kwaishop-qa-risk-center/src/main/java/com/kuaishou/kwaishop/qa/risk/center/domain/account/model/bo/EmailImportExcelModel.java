package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.kuaishou.kwaishop.qa.risk.center.utils.RegexUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.annotation.ExcelNotNull;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.annotation.ExcelStringFormat;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.ValidResultBO;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-30
 */
@Data
public class EmailImportExcelModel extends BaseExcelModel {

    @ExcelProperty("token")
    @ExcelStringFormat
    private String token;

    @ExcelNotNull
    @ExcelStringFormat
    @ExcelProperty("email")
    private String email;

    @ExcelNotNull
    @ExcelStringFormat
    @ExcelProperty("password")
    private String password;

    @ExcelStringFormat
    @ExcelProperty("kuaishou.api_st")
    private String apiSt;

    @ExcelStringFormat
    @ExcelNotNull
    @ExcelProperty("token_client_salt")
    private String tokenSalt;

    @ExcelStringFormat
    @ExcelNotNull
    @ExcelProperty("kuaishou.api_client_salt")
    private String apiSalt;

    @Override
    public ValidResultBO customValid() {
        ValidResultBO res =  ValidResultBO.buildDefault();
        if (!RegexUtils.isEmail(email)) {
            res.setResult(Boolean.FALSE);
            res.setFailMsg("邮箱格式不正确");
        }
        return res;
    }

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
