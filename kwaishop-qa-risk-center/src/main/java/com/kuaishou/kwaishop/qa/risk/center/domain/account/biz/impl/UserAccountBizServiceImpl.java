package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.impl;

import static com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode.BasicErrorCode.SERVER_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_EXIST_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_TEST_TYPE;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.API_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.CDN_URL_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.LOGIN_TYPE_NOT_RIGHT_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.NOT_AUTH_TO_UPDATE;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.OPERATOR_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PASSWORD_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.QUERY_TASK_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.QUERY_TASK_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.SCENE_NOT_RIGHT_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.TOKEN_NOT_EXIST_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.UID_NOT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringConfigKey.kresOpenApiUrl;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringListConfigKey.testAccountSceneList;
import static com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl.UserAuthServiceImpl.GET_ALL_SCORE_FOR_BUSINESS;
import static com.kuaishou.kwaishop.qa.risk.center.domain.account.service.impl.UserAuthServiceImpl.KWAISHOP_APOLLO_PINOCCHIO_CENTER;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.kuaishou.infra.passport.internal.sdk.common.PassportResponse;
import com.kuaishou.infra.passport.internal.sdk.service.PassportUserService;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.RentAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.UserAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAuthConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportAccountExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.ImportTokenExcelBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAuthBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserImportTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserLoginTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.AccountExcelImportFactory;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.AccountExcelImportService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAccountService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserLoginService;
import com.kuaishou.kwaishop.qa.risk.center.domain.auth.aops.AuthClear;
import com.kuaishou.kwaishop.qa.risk.center.domain.auth.service.AuthService;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.KlinkExcelImportService;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.service.SearchInfoService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.cache.EntityCacheService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.AccountManagementRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ApplyAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetAccountTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountExcelRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.PageUserAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateAllUserInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.ImportKlinkTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.utils.JsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.MathUtil;
import com.kuaishou.kwaishop.qa.risk.center.utils.OpenApiUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.KessUtil;
import com.kuaishou.kwaishop.qa.risk.center.utils.kess.dto.KessRun;
import com.kuaishou.old.util.StringUtil;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
@Lazy
@Service
@Slf4j
public class UserAccountBizServiceImpl implements UserAccountBizService {


    @Autowired
    private UserAccountService userAccountService;

    @Autowired
    private UserAccountConvert userAccountConvert;

    @Autowired
    private EntityCacheService entityCacheService;

    @Autowired
    private AccountExcelImportFactory excelImportFactory;

    @Autowired
    private AuthService authService;

    @Autowired
    private UserLoginService userLoginBizService;

    @Autowired
    private UserAuthConvert userAuthConvert;

    @Autowired
    private UserAuthBizService userAuthBizService;

    @Autowired
    private KlinkExcelImportService klinkExcelImportService;

    @Autowired
    private SearchInfoService searchInfoService;

    @Autowired
    private RentAccountBizService rentAccountBizService;

    @Autowired
    private PassportUserService passportUserService;

    private final Integer applyNum = 1941;

    @Override
    public List<UserAccountDTO> queryUserAccountList(QueryUserAccountRequest request) {
        UserAccountBO queryBO = userAccountConvert.buildQueryBO(request);
        List<UserAccountDO> resList = userAccountService.queryList(queryBO);
        List<UserAccountDTO> userAccountDTOS = buildAccountDTO(resList, request.getOperator());
        return buildAccountDTOByUserAuth(userAccountDTOS);
    }

    @Override
    public PageUserAccountDTO queryPageUserAccountList(QueryPageUserAccountRequest request) {
        UserAccountBO queryBO = userAccountConvert.buildQueryPageBO(request);
        // 先查权限表
        if (needUserAuthQuery(request)) {
            List<UserAuthDO> userAuthDOS = userAuthBizService.queryUserAuthRecords(userAuthConvert.buildUserAuthBOByRequest(request));
            // 如果是空
            if (userAuthDOS.isEmpty()) {
                queryBO.setUserIds(Collections.singletonList(0L));
            } else {
                queryBO.setUserIds(userAuthDOS.stream()
                        .map(UserAuthDO::getUserId)
                        .collect(Collectors.toList()));
            }
        }
        PageBO<UserAccountDO> resList = userAccountService.queryPageList(queryBO);
        // 根据规则排序，暂时不生效，后面再排查原因。
        sortPageDataByCustomRule(resList, request.getOperator());

        return buildPageAccountDTO(resList, request.getOperator());
    }

    /**
     * 暂时不生效的，后面再排查原因。
     *
     * @param resList
     * @param operator
     */
    private void sortPageDataByCustomRule(PageBO<UserAccountDO> resList, String operator) {
        List<UserAccountDO> data = resList.getData();
        List<String> accountLists = rentAccountBizService.queryListByOperator(RentAccountBO.builder()
                .borrower(operator)
                .build());
        // 如果是新人，没有借用数据，则通过全量的借用情况
        if (data != null && !data.isEmpty()) {
            // 统计 username 出现次数
            Map<String, Integer> frequencyMap = new HashMap<>();
            for (String account : accountLists) {
                frequencyMap.put(account, frequencyMap.getOrDefault(account, 0) + 1);
            }

            // 自定义 Comparator
            Comparator<UserAccountDO> customComparator = (u1, u2) -> {
                int u1Frequency = frequencyMap.getOrDefault(u1.getAccount(), 0);
                int u2Frequency = frequencyMap.getOrDefault(u2.getAccount(), 0);

                // 按照出现次数降序排序
                if (u1Frequency != u2Frequency) {
                    return Integer.compare(u2Frequency, u1Frequency);
                }

                // 出现次数相同或都为 0，保持原有顺序
                return 0;
            };

            // 对列表进行排序
            Collections.sort(data, customComparator);
        }
        resList.setData(data);
    }

    private boolean needUserAuthQuery(QueryPageUserAccountRequest request) {
        return request.getDistributorPermission() != 2 || request.getMerchantPermission() != 2
                || request.getLeaderPermission() != 2 || request.getPromoterPermission() != 2 || !request.getShopType().isEmpty();
    }

    @Override
    public List<Long> queryTestAccountList(GetTestAccountRequest request) {
        if (!testAccountSceneList.get().contains(request.getScene())) {
            throw new BizException(SCENE_NOT_RIGHT_ERROR);
        }
        List<UserAccountDO> res = userAccountService.queryList(new UserAccountBO());
        return res.stream().map(UserAccountDO::getUserId).collect(Collectors.toList());
    }

    @Override
    @AuthClear
    public void importAccountExcel(ImportAccountExcelRequest request) {
        ImportAccountExcelBO excelBO = userAccountConvert.buildExcelBO(request);
        excelCheck(excelBO);
        AccountExcelImportService importService = excelImportFactory.getService(excelBO.getImportType());
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        if (request.getImportType() == UserImportTypeEnum.EMAIL_IMPORT.getCode()
                || request.getImportType() == UserImportTypeEnum.PHONE_IMPORT.getCode()) {
            importService.getAccountDOList(excelBO).forEach(e -> {
                executorService.submit(() -> {
                    userAccountService.createAccount(e);
                    // 调用查询权限并插入权限表
                    userAuthBizService.insertAuthInfo(e.getUserId());
                });
            });
        } else {
            importService.getAccountDOList(excelBO).forEach(e -> {
                executorService.submit(() -> {
                    userAccountService.createAccount(e);
                });
            });
        }

        executorService.shutdown();
    }

    @Override
    @AuthClear
    public void importAccountToken(ImportAccountTokenRequest request) {
        ImportTokenExcelBO excelBO = userAccountConvert.buildTokenExcelBO(request);
        // 不做校验了。
        AccountExcelImportService importService = excelImportFactory.getService(UserImportTypeEnum.PHONE_IMPORT.getCode());
        // 借用
        importService.getAccountTokenDOList(excelBO).forEach(e -> {
            userAccountService.updateToken(e);
        });
    }

    @Override
    @AuthClear
    public void importKlinkToken(ImportKlinkTokenRequest request) {
        ImportTokenExcelBO excelBO = userAccountConvert.buildTokenExcelBO(request);
        klinkExcelImportService.getKlinkLoginDOList(excelBO).forEach(e -> {
            searchInfoService.updateUserKlinkLoginInfo(e);
        });

    }

    @Override
    public void applyAccount(ApplyAccountRequest request) throws Exception {
        //后续可加权限
        long startTime = System.currentTimeMillis();

        CompletableFuture<UserAccountDO> future1 = CompletableFuture.supplyAsync(() -> {
            return apply(request);
        });
        try {
            UserAccountDO accountDO = future1.get();
            if (accountDO == null) {
                throw new BizException(API_ERROR);
            }
            applyCheck(accountDO);

            CompletableFuture<Void> future2 = userAccountAsync(accountDO, request);
            // 异步等待账户创建和权限信息插入完成

            future2.get(60, TimeUnit.SECONDS);
            System.out.println("Account created and auth info inserted successfully");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("Account creation and auth info insertion interrupted: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("Account creation or auth info insertion failed: " + e.getCause().getMessage());
        }

        // 调用接口
        long endTime2 = System.currentTimeMillis();
        long duration2 = endTime2 - startTime;
        log.info("接口耗时: {} ms", duration2);

    }

    public CompletableFuture<Void> userAccountAsync(UserAccountDO accountDO, ApplyAccountRequest request) {
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            userAccountService.createAccount(accountDO);
        }).thenRunAsync(
                () -> {
                    userAuthBizService.insertAuthInfo(accountDO.getUserId());
                }
        ).thenRunAsync(
                () -> {
                    rentAccountBizService.rentAccount(request, accountDO);
                }
        );
        // 异步等待账户创建完成
        try {
            future.get(1, TimeUnit.SECONDS);
            System.out.println("Account created successfully");
        } catch (Exception e) {
            Thread.currentThread().interrupt();
        }
        return future;
    }


    public UserAccountDO apply(ApplyAccountRequest request) {
        HttpUtils httpUtils = OpenApiUtils.getHttpUtils();
        String url = kresOpenApiUrl.get() + "/openapi/scene/execute";
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + OpenApiUtils.getKresAccessToken("1941"));

        JsonObject params = new JsonObject();
        params.addProperty("id", applyNum);
        JsonArray jsonArray = new JsonArray();
        params.add("inputParams", jsonArray);
        try {
            Response response = httpUtils.post(url, headers, JsonUtils.toJsonString(params));
            if (!response.isSuccessful()) {
                throw new BizException(QUERY_TASK_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(QUERY_TASK_NULL);
            }
            String responseBody = response.body().string();
            log.info("[UserAccountBizServiceImpl] ApplyAccount request: {}", request);
            log.info("[UserAccountBizServiceImpl] ApplyAccount res: {}", responseBody);
            JsonObject jsonObject = JsonUtils.stringToJson(responseBody, JsonObject.class);
            UserAccountDO userAccountDO = userAccountConvert.parseUserAccount(jsonObject.get("data").getAsJsonArray());
            return userAccountDO;
        } catch (IOException e) {
            log.error("[UserAccountBizServiceImpl] ApplyAccount error,", e);
        }
        return null;
    }


    @Override
    public void accountManagement(AccountManagementRequest request) {
        // 判断有没有权限做更改
        List<UserAccountDO> userAccountDOS = userAccountService.queryAccountByUserId(request.getUserId());
        if (userAccountDOS.isEmpty()) {
            // 账户不存在
            throw new BizException(ACCOUNT_NOT_EXIST_ERROR);
        }
        String creator = userAccountDOS.get(0).getCreator();
        if (!creator.equals(request.getOperator())) {
            throw new BizException(NOT_AUTH_TO_UPDATE);
        }

        // 进行更新
        userAccountService.updateTeamId(request.getUserId(), request.getTeamId());

    }

    @Override
    @AuthClear
    public void importUserAccount(ImportUserAccountRequest request) {

        UserAccountBO accountBO = userAccountConvert.buildCreateBO(request);
        createCheck(accountBO);

        userAccountService.createAccount(userAccountConvert.buildCreateDO(accountBO));
        // 调用查询权限并插入权限表
        userAuthBizService.insertAuthInfo(request.getUserId());
    }


    @Override
    @AuthClear
    public String getAccountToken(GetAccountTokenRequest request) {
        List<UserAccountDO> userAccountDOS = userAccountService.queryAccountByUserId(request.getUserId());
        if (userAccountDOS.isEmpty()) {
            throw new BizException(TOKEN_NOT_EXIST_ERROR);
        }
        return userLoginBizService.getToken(userAccountConvert.buildAccountTokenBO(userAccountDOS.get(0)));
    }

    @Override
    public void updateAllUserInfo(UpdateAllUserInfoRequest request) {
        // 0.增加一步，先进行团队的更新
        // 1.分两步，一步是开通权限，一步是修改密码，权限。
        // 1.修改密码
        if (!StringUtil.isNullOrEmpty(request.getPassword())) {
            //
            userAccountService.updatePassword(request);
        }
        // 2.所有信息在这里处理。
        userAuthBizService.updateUserAllAuthInfo(request);
    }

    private void applyCheck(UserAccountDO userAccountDO) {
        if (userAccountDO.getUserId() == null) {
            throw new BizException(UID_NOT_EMPTY_ERROR);
        }
        if (StringUtils.isBlank(userAccountDO.getAccount())) {
            throw new BizException(ACCOUNT_EMPTY_ERROR);
        }
        if (StringUtils.isBlank(userAccountDO.getPassword())) {
            throw new BizException(PASSWORD_EMPTY_ERROR);
        }
    }

    private void createCheck(UserAccountBO accountBO) {


        if (StringUtils.isBlank(accountBO.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
        if (StringUtils.isBlank(accountBO.getAccount())) {
            throw new BizException(ACCOUNT_EMPTY_ERROR);
        }
        if (StringUtils.isBlank(accountBO.getPassword())) {
            throw new BizException(PASSWORD_EMPTY_ERROR);
        }

        //新增限制：禁止增量个人账号录入到账户池
        PassportResponse<Boolean> ksTestAccount = passportUserService.isKsTestAccount(accountBO.getUserId());
        if (ksTestAccount.isSuccess() && !ksTestAccount.getResult()) {
            throw new BizException(ACCOUNT_NOT_TEST_TYPE);
        }
//        if (!MathUtil.isNonZeroLong(accountBO.getCenterId())
//                || !MathUtil.isNonZeroLong(accountBO.getTeamId())) {
//            throw new BizException(CENTER_OR_TEAM_NOT_RIGHT_ERROR);
//        }
        //不对操作人，质量中心，团队id做校验。
//        authService.authPreCheck(accountBO.getOperator(), accountBO.getCenterId(), accountBO.getTeamId());
        if (!MathUtil.isNonZeroLong(accountBO.getUserId())) {
            throw new BizException(UID_NOT_EMPTY_ERROR);
        }
        // 判断双分是否大于0
//        if (checkShopScoreAndPromoterScore(String.valueOf(accountBO.getUserId()))) {
//            throw new BizException(SCORE_ERROR);
//        }

        if (String.valueOf(accountBO.getUserId()).equals("3")) {
            // 测试使用
            throw new BizException(SERVER_ERROR);
        }

        UserLoginTypeEnum loginTypeEnum = UserLoginTypeEnum.of(accountBO.getLoginType());
        if (loginTypeEnum == null || loginTypeEnum.equals(UserLoginTypeEnum.UNKNOWN)) {
            throw new BizException(LOGIN_TYPE_NOT_RIGHT_ERROR);
        }
        switch (loginTypeEnum) {
            case EMAIL_LOGIN:
                // 测试账号邮箱不一定规则，不进行邮箱校验
//                if (!RegexUtils.isEmail(accountBO.getAccount())) {
//                    throw new BizException(EMAIL_TYPE_NOT_RIGHT_ERROR);
//                }
                break;
            case CITY_86_LOGIN:
            case CITY_1264_LOGIN:
//                if (!RegexUtils.isMobile(accountBO.getAccount())) {
//                    throw new BizException(PHONE_TYPE_NOT_RIGHT_ERROR);
//                }
                // 测试手机号不一定按照规则，不进行手机号校验
                break;
            default:
                throw new BizException(LOGIN_TYPE_NOT_RIGHT_ERROR);
        }
    }

    private void excelCheck(ImportAccountExcelBO excelBO) {
        if (StringUtils.isBlank(excelBO.getOperator())) {
            throw new BizException(OPERATOR_EMPTY_ERROR);
        }
//        if (UserImportTypeEnum.of(excelBO.getImportType()) == null) {
//            throw new BizException(IMPORT_TYPE_NOT_RIGHT_ERROR);
//        }
        if (StringUtils.isBlank(excelBO.getCdnUrl())) {
            throw new BizException(CDN_URL_EMPTY_ERROR);
        }
//        UserAccountTypeEnum accountTypeEnum = UserAccountTypeEnum.of(excelBO.getAccountType());
//        if (accountTypeEnum == null) {
//            throw new BizException(ACCOUNT_TYPE_NOT_RIGHT_ERROR);
//        }
//        if (accountTypeEnum.equals(PERSONAL)) {
//            if (!MathUtil.isNonZeroLong(excelBO.getCenterId())
//                    || !MathUtil.isNonZeroLong(excelBO.getTeamId())) {
//                throw new BizException(CENTER_OR_TEAM_NOT_RIGHT_ERROR);
//            }
//            authService.authPreCheck(excelBO.getOperator(), excelBO.getCenterId(), excelBO.getTeamId());
//        }
    }

    public boolean checkShopScoreAndPromoterScore(String userId) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("seller_id", userId);

        KessRun kessRun = KessRun.builder()
                .jsonData(JsonUtils.jsonObjectToMap(jsonObject))
                .rpcName(KWAISHOP_APOLLO_PINOCCHIO_CENTER)
                .rpcMethod(GET_ALL_SCORE_FOR_BUSINESS)
                .build();
        try {
            String run = KessUtil.run(kessRun);
            JsonObject data = JsonUtils.stringToJson(run, JsonObject.class).getAsJsonObject("data");
            JsonObject shopScoreInfo = data.getAsJsonObject("shop_score_info");

            String shopScore = shopScoreInfo.getAsJsonObject("score_summary").get("score").getAsString();
            String promoterScore = data.getAsJsonObject("master_score_info").getAsJsonObject("score_summary").get("score").getAsString();

            if (shopScore.equals("0") && promoterScore.equals("0")) {
                // 需要获取到双分为0，才进行录入
                return true;
            }
        } catch (Exception e) {
            throw new BizException(SERVER_ERROR);
        }
        return false;
    }


    private PageUserAccountDTO buildPageAccountDTO(PageBO<UserAccountDO> pageBO, String op) {
        return PageUserAccountDTO.newBuilder()
                .setPageNo(pageBO.getPageNo())
                .setPageSize(pageBO.getPageSize())
                .setTotal(pageBO.getTotal())
                .addAllDetails(buildAccountDTOByUserAuth(buildAccountDTO(pageBO.getData(), op)))
                .build();
    }

    private List<UserAccountDTO> buildAccountDTO(List<UserAccountDO> dataList, String op) {
        Set<Long> entityIds = new HashSet<>();
        dataList.forEach(e -> {
            entityIds.add(e.getTeamId());
            entityIds.add(e.getCenterId());
        });
        Map<Long, EntityDO> entityDOMap = entityCacheService.batchQueryByIds(entityIds);
        List<UserAccountDTO> res = new ArrayList<>();
        dataList.forEach(e -> {
            EntityDO centerDO = entityDOMap.get(e.getCenterId());
            String centerName = centerDO == null ? StringUtils.EMPTY : centerDO.getName();
            EntityDO teamDO = entityDOMap.get(e.getTeamId());
            String teamName = teamDO == null ? StringUtils.EMPTY : teamDO.getName();
            res.add(userAccountConvert.buildDTO(e, centerName, teamName, op));
        });
        return res;
    }

    /**
     * 查询权限，组装成内容
     *
     * @param userAccountDTOS
     * @return
     */
    private List<UserAccountDTO> buildAccountDTOByUserAuth(List<UserAccountDTO> userAccountDTOS) {
        List<Long> userIds = userAccountDTOS.stream()
                .map(UserAccountDTO::getUserId)
                .collect(Collectors.toList());
        // 去查
        List<UserAuthDO> userAuthDOS = userAuthBizService.queryUserAuthRecords(UserAuthBO.builder()
                .userIds(userIds)
                .recruitingLeader(2)
                .distributorPermission(2)
                .merchantPermission(2)
                .celebrityPermission(2)
                .build());
        Map<Long, UserAuthDO> map = userAuthDOS.stream()
                .collect(Collectors.toMap(UserAuthDO::getUserId, Function.identity()));
        List<UserAccountDTO> userAccountDTOLists = new ArrayList<>();
        userAccountDTOS.forEach(userAccountDTO -> {
            userAccountDTOLists.add(userAccountConvert.buildUserAuthDTO(userAccountDTO, map));
        });
        return userAccountDTOLists;
    }
}
