package com.kuaishou.kwaishop.qa.risk.center.db.mapper.feature;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.feature.impl.FundsRiskFeatureSqlProvider;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureRequestDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface FundsRiskFeatureMapper extends BaseMapper<FundsRiskFeatureDO> {
    @Update("update funds_risk_feature_view set status = #{status}, update_time = #{updateTime}, updater = #{updater} "
            + "where feature_id = #{featureId}")
    void updateFundsRiskFeatureViewState(@Param("featureId") String featureId, @Param("status") Integer status,
                                         @Param("updateTime") Long updateTime, @Param("updater") String updater);

    @Select(" select * from funds_risk_feature_view"
            + "where feature_id = #{featureId}")
    FundsRiskFeatureDO queryByFeatureId(@Param("featureId") String featureId);

//    @Select(" select * from funds_risk_feature_view"
//            + "where is_risk = #{isRisk}")
//    KspayPageBO<FundsRiskFeatureDO> queryByIsRisk(@Param("isRisk") Integer isRisk);

    @Select("select * from funds_risk_feature_view "
            + "where department = #{department} "
            + "and risk_pass_status = #{riskPassStatus} "
            + "and update_time >= #{startTime} "
            + "and update_time <= #{endTime}")
    List<FundsRiskFeatureDO> queryNeedSendKimFeature(
            @Param("department") String department,
            @Param("riskPassStatus") Integer riskPassStatus,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime);

//    @Select("<script>"
//            + "SELECT * FROM funds_risk_feature_view WHERE status IN "
//            + "<foreach item='statusItem' collection='status' open='(' separator=',' close=')'>"
//            + "#{statusItem}"
//            + "</foreach>"
//            + " AND risk_pass_status = #{riskPassStatus}"
//            + " AND update_time >= #{startTime}"
//            + " AND update_time <= #{endTime}"
//            + "</script>")
//    List<FundsRiskFeatureDO> queryNeedSendKimFeatureError(@Param("status") Collection<Integer> status,
//                                                  @Param("riskPassStatus") Integer riskPassStatus,
//                                                  @Param("startTime") Long startTime,
//                                                  @Param("endTime") Long endTime);
    @Select(" select * from funds_risk_feature_view limit 20000")
    List<FundsRiskFeatureDO> queryAllFeatureNoCondition();

    @Update("update funds_risk_feature_view set updater = #{updater}, risk_pass_status = #{riskPassStatus}, "
            + "extra_data = #{extraData} "
            + "where feature_id = #{featureId}")
    int updateFeatureView(@Param("updater") String updater,
//                          @Param("updateTime") Long updateTime,
                          @Param("riskPassStatus") Integer riskPassStatus,
                          @Param("featureId") String featureId,
                          @Param("extraData") String extraData);

    @Update("update funds_risk_feature_view set status = #{status} where feature_id = #{featureId}")
    int updateFeatureViewStatusByFeatureId(@Param("status") Integer status,
                          @Param("featureId") String featureId);

    @Update("update funds_risk_feature_view set test_type = #{testType} "
            + "where feature_id = #{featureId}")
    int updateTestTypeByFeatureId(@Param("testType") Integer testType,
                                  @Param("featureId") String featureId);

    @Update({
            "UPDATE funds_risk_feature_view AS main_view",
            "JOIN funds_risk_feature_view_branch AS branch ON main_view.feature_id = branch.feature_view_id",
            "SET main_view.is_risk = 1",
            "WHERE branch.fund_risk_method_amount > 0"
    })
    void updateFeatureViewWithRisk();

    // fund_risk_method_amount为空的也按照是0处理
    @Update({
            "UPDATE funds_risk_feature_view AS main_view",
            "JOIN (",
            "SELECT feature_view_id",
            "FROM (",
            "SELECT feature_view_id",
            "FROM funds_risk_feature_view_branch",
            "WHERE (fund_risk_method_amount IS NULL OR fund_risk_method_amount = 0)",
            "GROUP BY feature_view_id",
            "HAVING (COUNT(*) > 1 AND COUNT(DISTINCT CASE WHEN fund_risk_method_amount IS NOT NULL THEN fund_risk_method_amount END) = 0)",
            "OR (COUNT(*) = 1 AND SUM(CASE WHEN fund_risk_method_amount IS NULL THEN 1 ELSE 0 END) = 1)) AS branch_summary_null_or_zero",
            "UNION ALL",
            "SELECT feature_view_id",
            "FROM (",
            "SELECT feature_view_id",
            "FROM funds_risk_feature_view_branch",
            "WHERE fund_risk_method_amount = 0",
            "GROUP BY feature_view_id",
            "HAVING COUNT(DISTINCT CASE WHEN fund_risk_method_amount != 0 THEN fund_risk_method_amount END) = 0) AS branch_summary_only_zero",
            ") AS combined_summary ON main_view.feature_id = combined_summary.feature_view_id",
            "SET main_view.is_risk = 2",
            "WHERE main_view.is_risk = 0"
    })
    void updateFeatureViewWithOutRisk();

    @Update("update funds_risk_feature_view set feature_name = #{featureName}, "
            + "team_id = #{teamId}, team_name = #{teamName}, "
            + "team_worker = #{teamWorker}, "
            + "status = #{status}, updater = #{updater} "
            + "where id = #{id}")
    int updateFeatureViewNoUpdateTimeOld(@Param("featureName") String featureName,
                          @Param("teamId") String teamID,
                          @Param("teamName") String teamName,
                          @Param("teamWorker") String teamWorker,
                          @Param("status") Integer status,
                          @Param("updater") String updater, @Param("id") String id);

//    @Update("update funds_risk_feature_view set feature_name = #{featureName}, department = #{department}, business_domain = #{businessDomain}, "
//            + "team_id = #{teamId}, team_name = #{teamName}, team_worker = #{teamWorker}, risk_coe = #{riskCoe}, "
//            + "status = #{status}, updater = #{updater} "
//            + "where id = #{id}")
//    int updateFeatureViewNoUpdateTime(@Param("featureName") String featureName,
//                                      @Param("department") String department,
//                                      @Param("businessDomain") String businessDomain,
//                                      @Param("teamId") String teamID,
//                                      @Param("teamName") String teamName,
//                                      @Param("teamWorker") String teamWorker,
//                                      @Param("riskCoe") String riskCoe,
//                                      @Param("status") Integer status,
//                                      @Param("updater") String updater, @Param("id") String id);
    @Select("select count(*) from funds_risk_feature_view a "
            + "where a.feature_id = #{featureId} and not exists (select 1 from funds_risk_feature_view_branch b "
            + "where a.feature_id = b.feature_view_id and (b.risk_status!=1 or b.audit_cover!=1)) "
            + "and (select count(*) from funds_risk_feature_view_branch b where a.feature_id = b.feature_view_id)>0")
    int isNeedUpdateRiskPassStatus(@Param("featureId") String featureId);

    @Update("update funds_risk_feature_view set risk_pass_status = 1 "
            + "where feature_id = #{featureId}")
    int updateRiskPassStatus(@Param("featureId") String featureId);

    @Update("update funds_risk_feature_view set online_confirm = #{onlineConfirm}, "
            + "updater = #{updater} "
            + "where feature_id = #{featureId}")
    int updateOnlineConfirm(@Param("featureId") String featureId,
                            @Param("onlineConfirm") Integer onlineConfirm,
                            @Param("updater") String updater);

    @Update("update funds_risk_feature_view set is_risk = 2 "
            + "where feature_id = #{featureId}")
    void updateAllFeaturesWithoutRisk(@Param("featureId") String featureId);

//    @SelectProvider(type = FundsRiskFeatureSqlProvider.class, method = "dynamicSql")
//    List<FundsRiskFeatureDO> queryFundsRiskFeaturesByPage(
//            @Param("isRisk") Integer isRisk,
//            @Param("startTime") Long startTime,
//            @Param("endTime") Long endTime,
//            @Param("offset") Integer offset,
//            @Param("limit") Integer limit,
//            @Param("status") List<Integer> status,
//            @Param("riskPassStatus") Integer riskPassStatus
//    );

    @SelectProvider(type = FundsRiskFeatureSqlProvider.class, method = "dynamicSql")
    List<FundsRiskFeatureDO> queryFundsRiskFeaturesByPage(@Param("params") FundsRiskFeatureRequestDO fundsRiskFeatureRequestDO);

    @SelectProvider(type = FundsRiskFeatureSqlProvider.class, method = "countSql")
    Long countFundsRiskFeatures(@Param("params") FundsRiskFeatureRequestDO fundsRiskFeatureRequestDO);

    @SelectProvider(type = FundsRiskFeatureSqlProvider.class, method = "allSql")
    List<FundsRiskFeatureDO> queryAllFundsRiskFeatures(@Param("params") FundsRiskFeatureRequestDO fundsRiskFeatureRequestDO);

    @Select("select * from funds_risk_feature_view where department = #{department} LIMIT 20000")
    List<FundsRiskFeatureDO> queryListByDepartment(@Param("department") String department);
}
