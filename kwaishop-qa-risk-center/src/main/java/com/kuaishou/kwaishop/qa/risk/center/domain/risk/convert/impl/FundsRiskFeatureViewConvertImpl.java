package com.kuaishou.kwaishop.qa.risk.center.domain.risk.convert.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskFeatureViewBranchDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.risk.FundsRiskFeatureViewDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.convert.FundsRiskFeatureViewConvert;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayFundsRiskFeatureViewBranchRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.risk.KspayFundsRiskFeatureViewRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-06-01
 */
@Component
@Slf4j
public class FundsRiskFeatureViewConvertImpl implements FundsRiskFeatureViewConvert {
    @Override
    public FundsRiskFeatureViewDO buildFundsRiskFeatureViewDo(Long id, KspayFundsRiskFeatureViewRequest request) {
        return FundsRiskFeatureViewDO.builder()
                .id(id)
                .featureId(request.getFeatureId())
                .featureName(request.getFeatureName())
                .department(request.getDepartment())
                .businessDomain(request.getBusinessDomain())
                .teamId(request.getTeamId())
                .teamName(request.getTeamName())
                .teamWorker(request.getTeamWorker())
                .status(Integer.parseInt(request.getStatus()))
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .creator("")
                .updater("")
                .build();
    }

    @Override
    public FundsRiskFeatureViewBranchDO buildFundsRiskFeatureViewBranchDo(Long id, KspayFundsRiskFeatureViewBranchRequest request) {
        if (request.getChangeMethodAmount().equals("") || request.getChangeMethodAmount().equals("")) {
            return FundsRiskFeatureViewBranchDO.builder()
                    .id(id)
                    .featureViewId(request.getFeatureViewId())
                    .repoName(request.getRepoName())
                    .branchName(request.getBranchName())
                    .methodEntrance(request.getMethodEntrance())
                    .accuracyUrl(request.getAccuracyUrl())
                    .testResult(request.getTestResult())
                    .riskTableFields(request.getRiskTableFields())
                    .createTime(System.currentTimeMillis())
                    .updateTime(System.currentTimeMillis())
                    .creator("")
                    .updater("")
                    .build();
        }
        return FundsRiskFeatureViewBranchDO.builder()
                .id(id)
                .featureViewId(request.getFeatureViewId())
                .repoName(request.getRepoName())
                .branchName(request.getBranchName())
                .methodEntrance(request.getMethodEntrance())
                .accuracyUrl(request.getAccuracyUrl())
                .changeMethodAmount(Integer.parseInt(request.getChangeMethodAmount()))
                .fundRiskMethodAmount(Integer.parseInt(request.getFundRiskMethodAmount()))
                .testResult(request.getTestResult())
                .riskTableFields(request.getRiskTableFields())
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .creator("")
                .updater("")
                .build();
    }

    @Override
    public FundsRiskFeatureViewDO buildUpdateFundsRiskFeatureViewDo(Long id, KspayFundsRiskFeatureViewRequest request) {
        return FundsRiskFeatureViewDO.builder()
                .id(id)
                .featureId(request.getFeatureId())
                .featureName(request.getFeatureName())
                .department(request.getDepartment())
                .businessDomain(request.getBusinessDomain())
                .teamId(request.getTeamId())
                .teamName(request.getTeamName())
                .teamWorker(request.getTeamWorker())
                .status(Integer.parseInt(request.getStatus()))
                .updateTime(System.currentTimeMillis())
                .updater("consumer")
                .build();
    }

    @Override
    public FundsRiskFeatureViewDO buildUpdateFundsRiskFeatureViewDoForTaskConsumer(Long id, KspayFundsRiskFeatureViewRequest request) {
        return FundsRiskFeatureViewDO.builder()
                .id(id)
                .featureId(request.getFeatureId())
                .featureName(request.getFeatureName())
                .department(request.getDepartment())
//                .businessDomain(request.getBusinessDomain())
                .teamId(request.getTeamId())
                .teamName(request.getTeamName())
                .teamWorker(request.getTeamWorker())
                .status(Integer.parseInt(request.getStatus()))
//                .updateTime(System.currentTimeMillis())
                .updater("consumer")
                .build();
    }

    @Override
    public FundsRiskFeatureViewDO buildUpdateFundsRiskFeatureViewDoForTask(Long id, KspayFundsRiskFeatureViewRequest request,
                                                                           FundsRiskFeatureViewDO fundsRiskFeatureViewDO) {
        log.info("build开始执行:{}", toJSON(fundsRiskFeatureViewDO));

        return FundsRiskFeatureViewDO.builder()
                .id(id)
                .featureId(request.getFeatureId())
                .featureName(request.getFeatureName())
                .department(request.getDepartment())
                .businessDomain(request.getBusinessDomain())
                .teamId(request.getTeamId())
                .teamName(request.getTeamName())
                .teamWorker(request.getTeamWorker())
                .status(Integer.parseInt(request.getStatus()))
                .isRisk(request.getIsRisk())
                .updateTime(fundsRiskFeatureViewDO.getUpdateTime())
                .updater("task")
                .build();
    }

    @Override
    public FundsRiskFeatureViewDO buildUpdateFundsRiskFeatureViewDoForTaskNew(Long id, KspayFundsRiskFeatureViewRequest request,
                                                                           FundsRiskFeatureViewDO fundsRiskFeatureViewDO) {
        log.info("build开始执行:{}", toJSON(fundsRiskFeatureViewDO));

        // 检查is_risk是否为0，如果是，则不构建
        if (request.getIsRisk() == 0) {
            return FundsRiskFeatureViewDO.builder()
                    .id(id)
                    .featureId(request.getFeatureId())
                    .featureName(request.getFeatureName())
                    .department(request.getDepartment())
                    .businessDomain(request.getBusinessDomain())
                    .teamId(request.getTeamId())
                    .teamName(request.getTeamName())
                    .teamWorker(request.getTeamWorker())
                    .status(Integer.parseInt(request.getStatus()))
                    .updateTime(fundsRiskFeatureViewDO.getUpdateTime())
                    .updater("task")
                    .build();
        }

        return FundsRiskFeatureViewDO.builder()
                .id(id)
                .featureId(request.getFeatureId())
                .featureName(request.getFeatureName())
                .department(request.getDepartment())
                .businessDomain(request.getBusinessDomain())
                .teamId(request.getTeamId())
                .teamName(request.getTeamName())
                .teamWorker(request.getTeamWorker())
                .status(Integer.parseInt(request.getStatus()))
                .isRisk(request.getIsRisk()) // 如果is_risk为0，这里不会执行
                .updateTime(fundsRiskFeatureViewDO.getUpdateTime())
                .updater("task")
                .build();
    }


    public FundsRiskFeatureViewDO buildUpdateFeatureTeamWorkerForTask(Long id, KspayFundsRiskFeatureViewRequest request) {
        return FundsRiskFeatureViewDO.builder()
                .id(id)
                .featureId(request.getFeatureId())
                .teamWorker(request.getTeamWorker())
                .build();
    }

    @Override
    public FundsRiskFeatureViewBranchDO buildUpdateFundsRiskFeatureViewBranchDo(Long id,
            KspayFundsRiskFeatureViewBranchRequest request) {
        if (request.getChangeMethodAmount().equals("") || request.getChangeMethodAmount().equals("")) {
            log.info("featureBranchUpdate无资损分支:{}", toJSON(request));
            return FundsRiskFeatureViewBranchDO.builder()
                    .id(id)
                    .featureViewId(request.getFeatureViewId())
                    .repoName(request.getRepoName())
                    .branchName(request.getBranchName())
                    .methodEntrance(request.getMethodEntrance())
                    .accuracyUrl(request.getAccuracyUrl())
                    .testResult(request.getTestResult())
                    .updateTime(System.currentTimeMillis())
                    .updater("task")
                    .build();
        }
        log.info("featureBranchUpdate有资损分支:{}", toJSON(request));
        return FundsRiskFeatureViewBranchDO.builder()
                .id(id)
                .featureViewId(request.getFeatureViewId())
                .repoName(request.getRepoName())
                .branchName(request.getBranchName())
                .methodEntrance(request.getMethodEntrance())
                .accuracyUrl(request.getAccuracyUrl())
                .changeMethodAmount(Integer.parseInt(request.getChangeMethodAmount()))
                .fundRiskMethodAmount(Integer.parseInt(request.getFundRiskMethodAmount()))
                .testResult(request.getTestResult())
                .updateTime(System.currentTimeMillis())
                .riskTableFields(request.getRiskTableFields())
                .updater("task")
                .build();
    }

    @Override
    public FundsRiskFeatureViewBranchDO buildUpdateFeatureViewBranchDo(Long id, KspayFundsRiskFeatureViewBranchRequest request,
                                                                       FundsRiskFeatureBranchDO fundsRiskFeatureBranchDO,
                                                                       boolean hasEntrance, boolean hasFields) {
        FundsRiskFeatureViewBranchDO branchDO = new FundsRiskFeatureViewBranchDO.Builder()
                .id(id)
                .featureViewId(request.getFeatureViewId())
                .repoName(request.getRepoName())
                .branchName(request.getBranchName())
                .accuracyUrl(request.getAccuracyUrl())
                .testResult(request.getTestResult())
                .updateTime(System.currentTimeMillis())
                .updater("task")
                .build();
        if (hasEntrance) {
            branchDO.setMethodEntrance(request.getMethodEntrance().length() < 10
                    ? fundsRiskFeatureBranchDO.getMethodEntrance() : request.getMethodEntrance());
        }
        if (hasFields) {
            branchDO.setRiskTableFields(request.getRiskTableFields().length() < 10
                    ? fundsRiskFeatureBranchDO.getRiskTableFields() : request.getRiskTableFields());
        }
        branchDO.setChangeMethodAmount(isEmpty(request.getChangeMethodAmount())
                ? fundsRiskFeatureBranchDO.getChangeMethodAmount() : Integer.valueOf(request.getChangeMethodAmount()));
        branchDO.setFundRiskMethodAmount(isEmpty(request.getFundRiskMethodAmount())
                ? fundsRiskFeatureBranchDO.getFundRiskMethodAmount() : Integer.valueOf(request.getFundRiskMethodAmount()));

        return branchDO;
    }

    private boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }
}
