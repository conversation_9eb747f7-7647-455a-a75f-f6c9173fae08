package com.kuaishou.kwaishop.qa.risk.center.db.mapper.account;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 16:45
 * @注释
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface TestAccountMapper extends BaseMapper<TestAccountDO> {


    @Select("SELECT * FROM test_account WHERE CAST(SUBSTRING(user_id, 1, 3) AS UNSIGNED) BETWEEN #{from} AND #{to}")
    List<TestAccountDO> queryBusinessAccountByRule(@Param("from") Integer from, @Param("to") Integer to);

    @Select({"<script>", "SELECT * FROM test_account  WHERE 1=1 ",
            "and id in ",
            "<foreach item='item' collection='ids' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    List<TestAccountDO> getByInterfaceListByIds(List<Long> ids);

    @Select("select * from test_account where kwai_id = #{kwai_id}")
    List<TestAccountDO> queryUserAccountByUserId(@Param("kwai_id") Long uid);


    @Update("UPDATE test_account "
            + "SET status = 2, deleted = 1 "
            + "WHERE kwai_id = #{kwai_id}")
    void deleteAccount(@Param("kwai_id") Long kwaiId);

}
