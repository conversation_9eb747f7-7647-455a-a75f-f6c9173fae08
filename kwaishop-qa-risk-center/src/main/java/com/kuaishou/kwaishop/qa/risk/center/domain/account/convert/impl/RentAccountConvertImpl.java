package com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.impl;

import java.time.Duration;
import java.time.Instant;
import java.util.Objects;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.RentAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.RentalStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserLoginTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.AccountRentalDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ExtendRentRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageRentRecordsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryRentRecordsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.RentAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.utils.date.LocalDateUtil;
import com.kuaishou.old.util.StringUtil;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-26
 */
@Component
public class RentAccountConvertImpl implements RentAccountConvert {

    @Override
    public RentAccountBO buildReturnAccountBO(RentAccountDO request) {
        return RentAccountBO.builder()
                .rentalStatus(RentalStatusEnum.RETURNED.getCode())
                .userId(request.getUserId())
                .dueTime(System.currentTimeMillis())
                .borrower(request.getBorrower())
                .id(request.getId())
                .build();
    }

    @Override
    public RentAccountBO buildRentAccountBO(UserAccountDO userAccountDO, RentAccountRequest request) {
        long rentalTime = System.currentTimeMillis();
        return RentAccountBO.builder()
                .borrower(request.getBorrower())
                .duration(request.getDuration())
                .centerId(userAccountDO.getCenterId())
                .teamId(userAccountDO.getTeamId())
                .loginType(userAccountDO.getLoginType())
                .rentalTime(rentalTime)
                .dueTime(Instant.ofEpochMilli(rentalTime).plus(Duration.ofHours(request.getDuration())).toEpochMilli())
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .userId(request.getUserId())
                .account(userAccountDO.getAccount())
                .password(userAccountDO.getPassword())
                .modifier(request.getBorrower())
                .build();
    }

    @Override
    public RentAccountBO buildRentAccountBO(TestAccountDO testAccountDO, long userId, String borrower, int duration,
            long bId) {
        long rentalTime = System.currentTimeMillis();
        return RentAccountBO.builder()
                .borrower(borrower)
                .duration(duration)
                .centerId(testAccountDO.getCenterId())
                .teamId(testAccountDO.getTeamId())
                .loginType(testAccountDO.getLoginType())
                .rentalTime(rentalTime)
                .dueTime(Instant.ofEpochMilli(rentalTime).plus(Duration.ofHours(duration)).toEpochMilli())
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .userId(userId)
                .bId(bId)
                .account(testAccountDO.getAccount())
                .password(testAccountDO.getPassword())
                .modifier(borrower)
                .build();
    }

    @Override
    public RentAccountBO buildRentAccountBO(UserAccountDO userAccountDO, long userId, String borrower, int duration) {
        long rentalTime = System.currentTimeMillis();
        return RentAccountBO.builder()
                .borrower(borrower)
                .duration(duration)
                .centerId(userAccountDO.getCenterId())
                .teamId(userAccountDO.getTeamId())
                .loginType(userAccountDO.getLoginType())
                .rentalTime(rentalTime)
                .dueTime(Instant.ofEpochMilli(rentalTime).plus(Duration.ofHours(duration)).toEpochMilli())
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .userId(userId)
                .account(userAccountDO.getAccount())
                .password(userAccountDO.getPassword())
                .modifier(borrower)
                .build();
    }


    @Override
    public RentAccountBO buildQueryPageRentRecords(QueryPageRentRecordsRequest request) {
        return RentAccountBO.builder()
                .borrower(request.getBorrower())
                .userId(request.getUserId())
                .loginType(request.getLoginType())
                .rentalStatus(request.getRentalStatus())
                .teamId(request.getTeamId())
                .centerId(request.getCenterId())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
    }

    @Override
    public AccountRentalDTO buildDTOByRentAccountDo(RentAccountDO data, String centerName, String teamName) {
        return AccountRentalDTO.newBuilder()
                .setCenterName(centerName)
                .setCenterId(data.getCenterId())
                .setTeamId(data.getTeamId())
                .setTeamName(teamName)
                .setBorrower(data.getBorrower())
                .setDuration(data.getDuration())
                .setRentalStatus(data.getRentalStatus())
                .setRentalTime(LocalDateUtil.milliToStringYMD(data.getRentalTime()))
                .setLoginType(data.getLoginType() == null ? UserLoginTypeEnum.UNKNOWN.getCode() : data.getLoginType())
                .setUserId(data.getUserId())
                .setId(data.getId())
                .setLoginTypeDesc(data.getLoginType() == null
                                  ?
                                  UserLoginTypeEnum.UNKNOWN.getDesc()
                                  :
                                  Objects.requireNonNull(UserLoginTypeEnum.of(data.getLoginType())).getDesc())
                .setRentalStatusDesc(Objects.requireNonNull(RentalStatusEnum.of(data.getRentalStatus())).getDesc())
                .setDueTime(LocalDateUtil.milliToStringYMD(data.getDueTime()))
                .setAccount(StringUtil.isNullOrEmpty(data.getAccount()) ? "" : data.getAccount())
                .setPassword(StringUtil.isNullOrEmpty(data.getPassword()) ? "" : data.getPassword())//后续数据库进行加密，直接返回即可
                .setUpdateTime(LocalDateUtil.milliToStringYMD(data.getUpdateTime()))
                .build();
    }

    @Override
    public RentAccountBO buildQueryRentRecords(QueryRentRecordsRequest request) {
        return RentAccountBO.builder()
                .borrower(request.getBorrower())
                .teamId(request.getTeamId())
                .centerId(request.getCenterId())
                .loginType(request.getLoginType())
                .rentalStatus(request.getRentalStatus())
                .userId(request.getUserId())
                .build();
    }

    @Override
    public RentAccountBO buildExtendRentRequest(ExtendRentRequest request, RentAccountDO rentAccountDO) {
        Long dueTime = rentAccountDO.getDueTime();
        int duration = request.getExtendTime();
        return RentAccountBO.builder()
                .userId(request.getUserId())
                .duration(duration)
                .dueTime(Instant.ofEpochMilli(dueTime).plus(Duration.ofHours(duration)).toEpochMilli())
                .updateTime(System.currentTimeMillis())
                .modifier(request.getBorrower())
                .id(rentAccountDO.getId())
                .build();
    }
}
