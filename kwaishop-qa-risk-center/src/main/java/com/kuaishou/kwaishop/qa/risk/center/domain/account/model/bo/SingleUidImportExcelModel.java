package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo;

import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.ValidResultBO;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-30
 */
@Data
public class SingleUidImportExcelModel extends BaseExcelModel {
    @Override
    public ValidResultBO customValid() {
        return ValidResultBO.buildDefault();
    }

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
