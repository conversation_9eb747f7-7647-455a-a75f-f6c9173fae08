package com.kuaishou.kwaishop.qa.risk.center.domain.stress.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StressScenarioRecordBO {
    /**
     * id
     */
    private Long id;

    /**
     * 风险明细名
     */
    private String name;

    /**
     * 风险类型
     */
    private Integer riskType;

    /**
     * 风险描述
     */
    private String riskDesc;

    /**
     * 校对类型
     */
    private Integer verifyType;

    /**
     * 风险链接
     */
    private String riskLink;

    /**
     * 演练报告
     */
    private String practiceReport;

    /**
     * 校对有效性
     */
    private Integer verifyEffective;

    /**
     * 预案链接
     */
    private String planLink;

    /**
     * 预案有效性
     */
    private Integer planEffective;

    /**
     * 风险是否覆盖
     */
    private Integer riskEffective;

    /**
     * 风险等级
     */
    private Integer level;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页容量
     */
    private Integer pageSize;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 视角类型
     */
    private Integer viewType;

    /**
     * 业务域
     */
    private Long businessId;

    /**
     * db操作类型
     */
    private Integer dbType;

    /**
     * 字段是否有对账覆盖
     */
    private Integer tableColumnCover;

    /**
     * 对账是否覆盖
     */
    private Integer riskCover;

    /**
     * 预案是否覆盖
     */
    private Integer planCover;

    /**
     * 业务链路
     */
    private String businessLine;

    /**
     * 业务链路描述
     */
    private String businessLineDesc;

    /**
     * db更新条件
     */
    private String dbChangeDesc;

    /**
     * 服务ksn
     */
    private String serviceKsn;

    /**
     * 服务方法名
     */
    private String serviceName;

    /**
     * git工程id
     */
    private Long gitId;

    /**
     * git工程名
     */
    private String gitName;

    /**
     * 库表
     */
    private String dbTableName;

    /**
     * 表字段
     */
    private String tableColumnName;

    /**
     * 故障影响面概述
     */
    private String breakdownEffectDesc;

    /**
     * 预案描述
     */
    private String planDesc;

    /**
     * 负责人
     */
    private String personLiable;

    /**
     * 资金敞口
     */
    private Long fundExp;

    /**
     * 业务域名称
     */
    private String businessName;
}
