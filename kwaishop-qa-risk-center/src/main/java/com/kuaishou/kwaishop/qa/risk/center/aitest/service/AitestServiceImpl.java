package com.kuaishou.kwaishop.qa.risk.center.aitest.service;

import static com.kuaishou.merchant.utils.ProtobufUtil.protoToJsonString;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.kuaishou.krpc.config.annotation.KrpcService;
import com.kuaishou.kwaishop.qa.risk.center.aitest.biz.AiProcessService;
import com.kuaishou.kwaishop.qa.risk.center.aitest.biz.UiCaseService;
import com.kuaishou.kwaishop.qa.risk.center.aitest.biz.UiSuiteService;
import com.kuaishou.kwaishop.qa.risk.center.aitest.convert.UiCaseConvert;
import com.kuaishou.kwaishop.qa.risk.center.aitest.convert.UiCaseResultConvert;
import com.kuaishou.kwaishop.qa.risk.center.aitest.convert.UiDataConvert;
import com.kuaishou.kwaishop.qa.risk.center.aitest.convert.UiSuiteCaseRelConvert;
import com.kuaishou.kwaishop.qa.risk.center.aitest.convert.UiSuiteConvert;
import com.kuaishou.kwaishop.qa.risk.center.aitest.convert.UiSuiteResultConvert;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.UiCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.UiCaseResultDO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.UiDataDO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.UiSuiteCaseRelDO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.UiSuiteDO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.UiSuiteResultDO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.bo.CompileCaseBO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.bo.QueryUiCaseBO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.bo.QueryUiSuiteBO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.bo.QueryUiSuiteResultBO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.bo.UiSuiteCaseWithUiDataBO;
import com.kuaishou.kwaishop.qa.risk.center.aitest.domain.enums.CaseType;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.ErrorCode;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.AddCasesIntoSuiteRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.AddCasesIntoSuiteResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.AiDebugRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.AiDebugResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.AiProcessCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.AiProcessCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateAssistCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateAssistCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateUiCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateUiCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateUiCaseResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateUiCaseResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateUiDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateUiDataResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateUiSuiteRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateUiSuiteResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateUiSuiteResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.CreateUiSuiteResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.DeleteUiCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.DeleteUiCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.DeleteUiDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.DeleteUiDataResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ExecuteCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ExecuteCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ExecuteSuiteRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ExecuteSuiteResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetCompileCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetCompileCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetUiCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetUiCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetUiCaseResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetUiCaseResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetUiDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetUiDataResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetUiSuiteRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetUiSuiteResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetUiSuiteResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.GetUiSuiteResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.KrpcAitestServiceGrpc.AitestServiceImplBaseV2;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiCaseResultsBySuiteResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiCaseResultsBySuiteResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiCaseResultsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiCaseResultsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiCasesRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiCasesResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiDataResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiSuiteCasesRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiSuiteCasesResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiSuiteResultsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiSuiteResultsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiSuitesRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.ListUiSuitesResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.OptNlpUiCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.OptNlpUiCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.RemoveCasesFromSuiteRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.RemoveCasesFromSuiteResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.SaveUiCaseResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.SaveUiCaseResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UiCaseResult;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UiSuite;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UiSuiteCaseWithUiData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UiSuiteResult;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UpdateUiCaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UpdateUiCaseResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UpdateUiCaseResultRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UpdateUiCaseResultResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UpdateUiDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UpdateUiDataResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UpdateUiSuiteCaseRelRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UpdateUiSuiteCaseRelResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UpdateUiSuiteRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.aitest.UpdateUiSuiteResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@KrpcService(registry = "custom-registry-config-kess")
public class AitestServiceImpl extends AitestServiceImplBaseV2 {

    @Resource
    private UiCaseService uiCaseService;

    @Resource
    private UiSuiteService uiSuiteService;

    @Resource
    private AiProcessService aiProcessService;


    @Override
    public CreateAssistCaseResponse createAssistCase(CreateAssistCaseRequest request) {
        log.info("[AitestServiceImpl] createAssistCase request: {}", protoToJsonString(request));
        try {
            UiCaseDO uiCaseDO = new UiCaseDO();
            uiCaseDO.setCaseDetail(request.getContent());
            uiCaseDO.setCaseAuthor(request.getUserName());
            uiCaseDO.setCaseName(StringUtils.isBlank(request.getName()) ? "" + LocalDateTime.now() : request.getName());
            uiCaseDO.setType(CaseType.RECORDING.getCode());
            JsonObject dataJson = new JsonObject();
            JsonObject entranceJson = new JsonObject();
            if (StringUtils.isNotBlank(request.getEntrance())) {
                URL url = new URL(request.getEntrance());
                String host = url.getHost();
                String entrancePath = url.getPath();

                entranceJson.addProperty("host", host);
                entranceJson.addProperty("entrancePath", entrancePath);
                entranceJson.addProperty("orignPath", request.getEntrance());
                dataJson.add("entrance", entranceJson);
            }
            Gson gson = new GsonBuilder().setPrettyPrinting().create();
            uiCaseDO = uiCaseService.createUiCase(uiCaseDO);
            UiDataDO uiDataDO = UiDataDO.builder()
                    .createTime(System.currentTimeMillis())
                    .dataName(request.getName() + request.getUserName() + LocalDateTime.now())
                    .params(entranceJson.toString())
                    .keyId(request.getAccountId())
                    .keyName(request.getAccountName())
                    .keyValue(request.getAccountPwd())
                    .params(gson.toJson(entranceJson))
                    .uiCaseId(uiCaseDO.getId())
                    .build();

            Long dataId = uiCaseService.insertData(uiDataDO);
            uiCaseDO.setUiDataId(dataId);
            uiCaseService.updateUiCase(uiCaseDO);
            return CreateAssistCaseResponse.newBuilder().setResult(1).setUiCase(UiCaseConvert.convertUiCaseDTO(uiCaseDO)).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] createAssistCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateAssistCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] createAssistCase error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateAssistCaseResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public CreateUiCaseResponse createUiCase(CreateUiCaseRequest request) {
        log.info("[AitestServiceImpl] createUiCase request: {}", protoToJsonString(request));
        try {
            UiCaseDO uiCaseDO = UiCaseConvert.convertUiCaseDO(request.getUiCase());
            uiCaseDO = uiCaseService.createUiCase(uiCaseDO);
            if (uiCaseDO == null) {
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode(), "");
            }
            return CreateUiCaseResponse.newBuilder().setResult(1).setUiCase(UiCaseConvert.convertUiCaseDTO(uiCaseDO)).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] createUiCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateUiCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] createUiCase error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateUiCaseResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public CreateUiSuiteResponse createUiSuite(CreateUiSuiteRequest request) {
        log.info("[AitestServiceImpl] createUiSuite request: {}", protoToJsonString(request));
        try {
            UiSuiteDO uiSuiteDO = UiSuiteConvert.convertToDO(request.getUiSuite());
            uiSuiteDO = uiSuiteService.createSuite(uiSuiteDO);
            if (uiSuiteDO == null) {
                throw new BizException(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode(), "");
            }
            return CreateUiSuiteResponse.newBuilder().setResult(1).setUiSuite(UiSuiteConvert.convertToDto(uiSuiteDO)).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] createUiSuite bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateUiSuiteResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] createUiSuite error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateUiSuiteResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public GetUiCaseResponse getUiCase(GetUiCaseRequest request) {
        log.info("[AitestServiceImpl] getUiCase request: {}", protoToJsonString(request));
        try {
            UiCaseDO uiCaseDO = uiCaseService.getUiCaseById(request.getId());
            if (uiCaseDO == null) {
                throw new BizException(0, "cant't find element");
            }
            return GetUiCaseResponse.newBuilder().setResult(1).setUiCase(UiCaseConvert.convertUiCaseDTO(uiCaseDO)).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] getUiCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return GetUiCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] getUiCase error, req: {}, exception: ", protoToJsonString(request), e);
            return GetUiCaseResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateUiCaseResponse updateUiCase(UpdateUiCaseRequest request) {
        log.info("[AitestServiceImpl] updateUiCase request: {}", protoToJsonString(request));
        try {
            UiCaseDO uiCaseDO = UiCaseConvert.convertUiCaseDO(request.getUiCase());
            boolean ret = uiCaseService.updateUiCase(uiCaseDO);
            uiCaseDO.setCreateTime(null);
            uiCaseDO.setType(null);
            return UpdateUiCaseResponse.newBuilder().setResult(ret ? 1 : 0).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] updateUiCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUiCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] updateUiCase error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUiCaseResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    /**
     * 软删除UiCase
     *
     * @param request
     * @return
     */
    @Override
    public DeleteUiCaseResponse deleteUiCase(DeleteUiCaseRequest request) {
        log.info("[AitestServiceImpl] deleteUiCase request: {}", protoToJsonString(request));
        try {

            uiCaseService.deleteUiCase(request.getId());

            return DeleteUiCaseResponse.newBuilder().setResult(1).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] deleteUiCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteUiCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] deleteUiCase error, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteUiCaseResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ListUiCasesResponse listUiCases(ListUiCasesRequest request) {
        log.info("[AitestServiceImpl] listUiCases request: {}", protoToJsonString(request));
        try {
            QueryUiCaseBO queryBO = QueryUiCaseBO.builder().pageNum(request.getPageNumber()).pageSize(request.getPageSize())
                    .caseName(request.getCaseName()).type(request.getTypeList()).caseAuthor(request.getCaseAuthor()).build();
            Page<UiCaseDO> ret = uiCaseService.listUiCases(queryBO);

            return ListUiCasesResponse.newBuilder().setResult(1).setTotalCount((int) ret.getTotal())
                    .addAllUiCases(UiCaseConvert.convertUiCaseDTOList(ret.getRecords())).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] listUiCases bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiCasesResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] listUiCases error, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiCasesResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ListUiSuitesResponse listUiSuites(ListUiSuitesRequest request) {
        log.info("[AitestServiceImpl] listUiSuites request: {}", protoToJsonString(request));
        try {
            QueryUiSuiteBO queryBO = QueryUiSuiteBO.builder().pageNum(request.getPageNumber()).pageSize(request.getPageSize())
                    .suiteName(request.getSuiteName()).suiteDesc(request.getSuiteDesc()).suiteAuthor(request.getSuiteAuthor()).build();
            Page<UiSuiteDO> ret = uiSuiteService.listUiSuites(queryBO);

            return ListUiSuitesResponse.newBuilder().setResult(1).setTotalCount((int) ret.getTotal())
                    .addAllUiSuites(UiSuiteConvert.convertToDtoList(ret.getRecords())).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] listUiSuites bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiSuitesResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] listUiSuites error, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiSuitesResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public ListUiDataResponse listUiData(ListUiDataRequest request) {
        log.info("[AitestServiceImpl] listUiData request: {}", protoToJsonString(request));
        try {
            List<UiDataDO> list = uiCaseService.listUiDatas(request.getCaseId());


            return ListUiDataResponse.newBuilder().setResult(1).addAllUiDataList(UiDataConvert.convertToDTOList(list)).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] listUiData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiDataResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] listUiData error, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiDataResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ListUiCaseResultsResponse listUiCaseResults(ListUiCaseResultsRequest request) {
        log.info("[AitestServiceImpl] listUiCaseResults request: {}", protoToJsonString(request));
        try {
            Page<UiCaseResultDO> list = uiSuiteService.listUiCaseResults(request.getPageNumber(), request.getPageSize(), request.getUiCaseId());

            return ListUiCaseResultsResponse.newBuilder().setResult(1)
                    .addAllUiCaseResultList(UiCaseResultConvert.convertUiCaseResultDTOList(list.getRecords()))
                    .setTotalCount((int) list.getTotal())
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] listUiCaseResults bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiCaseResultsResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] listUiCaseResults error, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiCaseResultsResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ListUiSuiteResultsResponse listUiSuiteResults(ListUiSuiteResultsRequest request) {
        log.info("[AitestServiceImpl] listUiSuiteResults request: {}", protoToJsonString(request));
        try {
            QueryUiSuiteResultBO queryBO = QueryUiSuiteResultBO.builder()
                    .uiSuiteId(request.getUiSuiteId())
                    .uiSuiteName(request.getUiSuiteName())
                    .pageNum(request.getPageNumber())
                    .statusList(request.getStatusList())
                    .executeStatusList(request.getExecuteStatusList())
                    .fromTime(request.getFromTime())
                    .toTime(request.getToTime())
                    .executor(request.getExecutor())
                    .pageSize(request.getPageSize())
                    .build();
            Page<UiSuiteResultDO> list = uiSuiteService.listUiSuiteResults(queryBO);

            return ListUiSuiteResultsResponse.newBuilder().setResult(1)
                    .addAllUiSuiteResultList(UiSuiteResultConvert.convertToDTOList(list.getRecords())).setTotalCount((int) list.getTotal()).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] listUiSuiteResults bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiSuiteResultsResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] listUiSuiteResults error, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiSuiteResultsResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    //查询Suite下
    @Override
    public ListUiSuiteCasesResponse listUiSuiteCases(ListUiSuiteCasesRequest request) {
        log.info("[AitestServiceImpl] ListUiSuiteCases request: {}", protoToJsonString(request));
        try {
            Page<UiSuiteCaseWithUiDataBO> uiCaseDOPage = uiSuiteService.listUiSuiteCases(request.getPageNumber(),
                    request.getPageNumber(), request.getUiSuiteId());
            List<UiSuiteCaseWithUiData> list = new ArrayList<>();

            Gson gson = new Gson();
            for (UiSuiteCaseWithUiDataBO item : uiCaseDOPage.getRecords()) {
                UiSuiteCaseWithUiData.Builder builder = UiSuiteCaseWithUiData.newBuilder()
                        .setUiSuiteId(item.getSuiteId())
                        .setId(item.getId())
                        .setUiCase(UiCaseConvert.convertUiCaseDTO(item.getUiCaseDO()));

                if (item.getUiDataDO() != null) {
                    builder.setUiData(UiDataConvert.convertToDTO(item.getUiDataDO()));
                } else {
                    builder.setUiData(UiDataConvert.convertToDTO(UiDataDO.builder().build()));
                } // 如果没有设置 ui_data，protobuf 会处理这个情况

                UiSuiteCaseWithUiData uiSuiteCaseWithUiData = builder.build();
                list.add(uiSuiteCaseWithUiData);
            }

            return ListUiSuiteCasesResponse.newBuilder().setResult(1).setTotalCount((int) uiCaseDOPage.getTotal()).addAllUiCaseDataList(list).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] listUiSuiteResults bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiSuiteCasesResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] listUiSuiteResults error, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiSuiteCasesResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public AddCasesIntoSuiteResponse addCasesIntoSuite(AddCasesIntoSuiteRequest request) {
        log.info("[AitestServiceImpl] addCasesIntoSuite request: {}", protoToJsonString(request));
        try {
            int ret = uiSuiteService.addCasesIntoSuite(request.getUiSuiteId(), request.getUiCaseIdList());
            log.info("[AitestServiceImpl] addCasesIntoSuite response: {}", ret);
            return AddCasesIntoSuiteResponse.newBuilder().setResult(1).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] addCasesIntoSuite bizError, req: {}, exception: ", protoToJsonString(request), e);
            return AddCasesIntoSuiteResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] addCasesIntoSuite error, req: {}, exception: ", protoToJsonString(request), e);
            return AddCasesIntoSuiteResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public RemoveCasesFromSuiteResponse removeCasesFromSuite(RemoveCasesFromSuiteRequest request) {
        log.info("[AitestServiceImpl] removeCasesFromSuite request: {}", protoToJsonString(request));
        try {
            int ret = uiSuiteService.removeCasesFromSuite(request.getUiSuiteId(), request.getUiCaseIdList());
            log.info("[AitestServiceImpl] removeCasesFromSuite response: {}", ret);
            return RemoveCasesFromSuiteResponse.newBuilder().setResult(1).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] removeCasesFromSuite bizError, req: {}, exception: ", protoToJsonString(request), e);
            return RemoveCasesFromSuiteResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] removeCasesFromSuite error, req: {}, exception: ", protoToJsonString(request), e);
            return RemoveCasesFromSuiteResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }


    @Override
    public UpdateUiCaseResultResponse updateUiCaseResult(UpdateUiCaseResultRequest request) {
        log.info("[AitestServiceImpl] updateUiCaseResult request: {}", protoToJsonString(request));
        try {
            UiCaseResultDO caseResultDO = UiCaseResultConvert.convertToEntity(request.getUiCaseResult());
            boolean ret = uiSuiteService.updateSuiteCaseResult(caseResultDO);
            log.info("[AitestServiceImpl] updateUiCaseResult response: {}", ret);
            return UpdateUiCaseResultResponse.newBuilder().setResult(1).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] updateUiCaseResult bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUiCaseResultResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] updateUiCaseResult error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUiCaseResultResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ExecuteSuiteResponse executeSuite(ExecuteSuiteRequest request) {
        log.info("[AitestServiceImpl] executeSuite request: {}", protoToJsonString(request));
        try {

            long ret = uiSuiteService.executeSuite(request.getUiSuiteId(), request.getTempEnv(), request.getTempLane(), request.getExecutor());
            log.info("[AitestServiceImpl] executeSuite response: {}", ret);
            return ExecuteSuiteResponse.newBuilder().setResult(1).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] executeSuite bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ExecuteSuiteResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] executeSuite error, req: {}, exception: ", protoToJsonString(request), e);
            return ExecuteSuiteResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ExecuteCaseResponse executeCase(ExecuteCaseRequest request) {
        log.info("[AitestServiceImpl] ExecuteCase request: {}", protoToJsonString(request));
        try {
            if (request.getUiCaseId() == 0) {
                return ExecuteCaseResponse.newBuilder().setResult(1).setErrorMsg("unknow case").build();
            }
            boolean ret =
                    uiSuiteService.executeCase(request.getUiCaseId(), request.getTempEnv(), request.getTempLane(), request.getUiDataId(),
                            request.getExecutor());
            log.info("[AitestServiceImpl] ExecuteCase response: {}", ret);
            return ExecuteCaseResponse.newBuilder().setResult(1).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] ExecuteCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ExecuteCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] executeCase error, req: {}, exception: ", protoToJsonString(request), e);
            return ExecuteCaseResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public GetUiSuiteResponse getUiSuite(GetUiSuiteRequest request) {
        log.info("[AitestServiceImpl] getUiSuite request: {}", protoToJsonString(request));
        try {
            UiSuiteDO uiSuiteDO = uiSuiteService.getSuite(request.getId());
            UiSuite uiSuite = UiSuiteConvert.convertToDto(uiSuiteDO);
            return GetUiSuiteResponse.newBuilder().setResult(1).setUiSuite(uiSuite).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] getUiSuite bizError, req: {}, exception: ", protoToJsonString(request), e);
            return GetUiSuiteResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] getUiSuite error, req: {}, exception: ", protoToJsonString(request), e);
            return GetUiSuiteResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public GetUiCaseResultResponse getUiCaseResult(GetUiCaseResultRequest request) {
        log.info("[AitestServiceImpl] getUiCaseResult request: {}", protoToJsonString(request));
        try {
            UiCaseResultDO uiSuiteDO = uiSuiteService.getCaseResult(request.getId());
            UiCaseResult uiCaseResult = UiCaseResultConvert.convertToDto(uiSuiteDO);
            return GetUiCaseResultResponse.newBuilder().setResult(1).setUiCaseResult(uiCaseResult).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] getUiCaseResult bizError, req: {}, exception: ", protoToJsonString(request), e);
            return GetUiCaseResultResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] getUiCaseResult error, req: {}, exception: ", protoToJsonString(request), e);
            return GetUiCaseResultResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public GetUiSuiteResultResponse getUiSuiteResult(GetUiSuiteResultRequest request) {
        log.info("[AitestServiceImpl] getUiSuiteResult request: {}", protoToJsonString(request));
        try {
            UiSuiteResultDO uiSuiteResultDO = uiSuiteService.getSuiteResult(request.getId());
            UiSuiteResult uiCaseResult = UiSuiteResultConvert.convertToDTO(uiSuiteResultDO);
            return GetUiSuiteResultResponse.newBuilder().setResult(1).setUiSuiteResult(uiCaseResult).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] getUiSuiteResult bizError, req: {}, exception: ", protoToJsonString(request), e);
            return GetUiSuiteResultResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] getUiSuiteResult error, req: {}, exception: ", protoToJsonString(request), e);
            return GetUiSuiteResultResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateUiSuiteResponse updateUiSuite(UpdateUiSuiteRequest request) {
        log.info("[AitestServiceImpl] getUiSuiteResult request: {}", protoToJsonString(request));
        try {
            UiSuite uiSuite = request.getUiSuite();
            uiSuiteService.updateSuite(UiSuiteConvert.convertToDO(uiSuite));
            return UpdateUiSuiteResponse.newBuilder().setResult(1).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] getUiSuiteResult bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUiSuiteResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] getUiSuiteResult error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUiSuiteResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public CreateUiCaseResultResponse createUiCaseResult(CreateUiCaseResultRequest request) {
        log.info("[AitestServiceImpl] createUiCaseResult request: {}", protoToJsonString(request));
        try {
            long id = uiSuiteService.createUiCaseResult(UiCaseResultConvert.convertToEntity(request.getUiCaseResult()));
            return CreateUiCaseResultResponse.newBuilder().setResult(1).setId(id).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] getUiSuiteResult bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateUiCaseResultResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] getUiSuiteResult error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateUiCaseResultResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public CreateUiSuiteResultResponse createUiSuiteResult(CreateUiSuiteResultRequest request) {
        log.info("[AitestServiceImpl] CreateUiSuiteResult request: {}", protoToJsonString(request));
        try {
            long id = uiSuiteService.createUiSuiteResult(UiSuiteResultConvert.convertToDO(request.getUiSuiteResult()));
            return CreateUiSuiteResultResponse.newBuilder().setResult(1).setUiSuiteResultId(id).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] CreateUiSuiteResult bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateUiSuiteResultResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] CreateUiSuiteResult error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateUiSuiteResultResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public GetCompileCaseResponse getCompileCase(GetCompileCaseRequest request) {
        log.info("[AitestServiceImpl] getCompileCase request: {}", protoToJsonString(request));
        try {
            CompileCaseBO compileCaseBO = uiCaseService.getCompileCase(request.getUiCaseId());
            return GetCompileCaseResponse.newBuilder().setResult(1).setCompileData(compileCaseBO.getCompileContent())
                    .setOriginAiData(compileCaseBO.getOriginAiContent())
                    .setOriginData(compileCaseBO.getOriginDetailContent())
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] getCompileCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return GetCompileCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] getCompileCase error, req: {}, exception: ", protoToJsonString(request), e);
            return GetCompileCaseResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public AiProcessCaseResponse aiProcessCase(AiProcessCaseRequest request) {
        log.info("[AitestServiceImpl] aiProcessCase request: {}", protoToJsonString(request));
        try {
            uiSuiteService.aiResultProcess(request.getUiCaseId());
            return AiProcessCaseResponse.newBuilder().setResult(1)
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] aiProcessCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return AiProcessCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] aiProcessCase error, req: {}, exception: ", protoToJsonString(request), e);
            return AiProcessCaseResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public CreateUiDataResponse createUiData(CreateUiDataRequest request) {
        log.info("[AitestServiceImpl] createUiData request: {}", protoToJsonString(request));
        try {
            long id = uiCaseService.insertData(UiDataConvert.convertToDomain(request.getUiData()));
            UiDataDO uiDataDO = uiCaseService.getUiData(id);
            return CreateUiDataResponse.newBuilder().setResult(1).setUiData(UiDataConvert.convertToDTO(uiDataDO))
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] createUiData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return CreateUiDataResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] createUiData error, req: {}, exception: ", protoToJsonString(request), e);
            return CreateUiDataResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateUiDataResponse updateUiData(UpdateUiDataRequest request) {
        log.info("[AitestServiceImpl] updateUiData request: {}", protoToJsonString(request));
        try {
            uiCaseService.updataUiData(UiDataConvert.convertToDomain(request.getUiData()));
            UiDataDO uiDataDO = uiCaseService.getUiData(request.getUiData().getId());
            return UpdateUiDataResponse.newBuilder().setResult(1).setUiData(UiDataConvert.convertToDTO(uiDataDO))
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] updateUiData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUiDataResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] updateUiData error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUiDataResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public GetUiDataResponse getUiData(GetUiDataRequest request) {
        log.info("[AitestServiceImpl] getUiData request: {}", protoToJsonString(request));
        try {
            UiDataDO uiDataDO = uiCaseService.getUiData(request.getId());
            return GetUiDataResponse.newBuilder()
                    .setResult(1)
                    .setUiData(UiDataConvert.convertToDTO(uiDataDO))
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] getUiData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return GetUiDataResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] getUiData error, req: {}, exception: ", protoToJsonString(request), e);
            return GetUiDataResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public DeleteUiDataResponse deleteUiData(DeleteUiDataRequest request) {
        log.info("[AitestServiceImpl] deleteUiData request: {}", protoToJsonString(request));
        try {
            long id = request.getId();
            uiCaseService.updataUiData(UiDataDO.builder().id(id).deleted(1).updateTime(System.currentTimeMillis()).build());
            return DeleteUiDataResponse.newBuilder()
                    .setResult(1)
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] deleteUiData bizError, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteUiDataResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] deleteUiData error, req: {}, exception: ", protoToJsonString(request), e);
            return DeleteUiDataResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public UpdateUiSuiteCaseRelResponse updateUiSuiteCaseRel(UpdateUiSuiteCaseRelRequest request) {
        log.info("[AitestServiceImpl] updateUiSuiteCaseRel request: {}", protoToJsonString(request));
        try {

            UiSuiteCaseRelDO uiSuiteCaseRelDO = UiSuiteCaseRelConvert.convertUiSuiteCaseRelDO(request.getUiSuiteCaseRel());
            uiSuiteService.updateSuiteCaseDataRel(uiSuiteCaseRelDO);
            return UpdateUiSuiteCaseRelResponse.newBuilder()
                    .setResult(1)
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] updateUiSuiteCaseRel bizError, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUiSuiteCaseRelResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] updateUiSuiteCaseRel error, req: {}, exception: ", protoToJsonString(request), e);
            return UpdateUiSuiteCaseRelResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public SaveUiCaseResultResponse saveUiCaseResult(SaveUiCaseResultRequest request) {
        log.info("[AitestServiceImpl] saveUiCaseResult request: {}", protoToJsonString(request));
        try {

            UiCaseResultDO caseResultDO = UiCaseResultConvert.convertToEntity(request.getUiCaseResult());
            uiSuiteService.saveCaseResult(caseResultDO);
            return SaveUiCaseResultResponse.newBuilder()
                    .setResult(1)
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] saveUiCaseResult bizError, req: {}, exception: ", protoToJsonString(request), e);
            return SaveUiCaseResultResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] saveUiCaseResult error, req: {}, exception: ", protoToJsonString(request), e);
            return SaveUiCaseResultResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public AiDebugResponse aiDebug(AiDebugRequest request) {
        log.info("[AitestServiceImpl] aiDebug request: {}", protoToJsonString(request));
        try {
            //String model, String role, String content, String operator, String biz
            String ret = aiProcessService.aiDebug(request.getModel(), request.getRole(), request.getContent(), request.getOperator(),
                    request.getBiz(), request.getUrl());
            return AiDebugResponse.newBuilder()
                    .setResult(1)
                    .setContent(ret)
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] aiDebug bizError, req: {}, exception: ", protoToJsonString(request), e);
            return AiDebugResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] aiDebug error, req: {}, exception: ", protoToJsonString(request), e);
            return AiDebugResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    @Override
    public ListUiCaseResultsBySuiteResultResponse listUiCaseResultsBySuiteResult(ListUiCaseResultsBySuiteResultRequest request) {
        log.info("[AitestServiceImpl] listUiCaseResultsBySuiteResult request: {}", protoToJsonString(request));
        try {
            //String model, String role, String content, String operator, String biz
            Page<UiCaseResultDO> list = uiSuiteService.listUiCaseResultsBySuiteResultId(request.getPageNumber(), request.getPageSize(),
                    request.getUiSuiteResultId());
            return ListUiCaseResultsBySuiteResultResponse.newBuilder().setResult(1)
                    .addAllUiCaseResultList(UiCaseResultConvert.convertUiCaseResultDTOList(list.getRecords()))
                    .setTotalCount((int) list.getTotal()).build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] listUiCaseResultsBySuiteResult bizError, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiCaseResultsBySuiteResultResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] listUiCaseResultsBySuiteResult error, req: {}, exception: ", protoToJsonString(request), e);
            return ListUiCaseResultsBySuiteResultResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }

    //  rpc OptNlpUiCase(OptNlpUiCaseRequest) returns (OptNlpUiCaseResponse);
    @Override
    public OptNlpUiCaseResponse optNlpUiCase(OptNlpUiCaseRequest request) {
        log.info("[AitestServiceImpl] optNlpUiCase request: {}", protoToJsonString(request));
        try {
            //String model, String role, String content, String operator, String biz
            String optCaseStr = uiCaseService.optNlpUiCase(request.getCaseId(), request.getCaseContent());
            return OptNlpUiCaseResponse.newBuilder().setResult(1)
                    .setContent(optCaseStr)
                    .build();
        } catch (BizException e) {
            log.error("[AitestServiceImpl] optNlpUiCase bizError, req: {}, exception: ", protoToJsonString(request), e);
            return OptNlpUiCaseResponse.newBuilder()
                    .setResult(e.getCode())
                    .setErrorMsg(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[AitestServiceImpl] optNlpUiCase error, req: {}, exception: ", protoToJsonString(request), e);
            return OptNlpUiCaseResponse.newBuilder()
                    .setResult(ErrorCode.BasicErrorCode.SERVER_ERROR.getCode())
                    .setErrorMsg(ErrorCode.BasicErrorCode.SERVER_ERROR.getMessage())
                    .build();
        }
    }
}
