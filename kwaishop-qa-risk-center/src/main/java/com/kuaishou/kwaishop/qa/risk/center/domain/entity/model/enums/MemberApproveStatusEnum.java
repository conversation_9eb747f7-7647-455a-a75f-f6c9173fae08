package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-11
 */
public enum MemberApproveStatusEnum {

    UNKNOWN(0, "未知状态"),
    WAIT(10, "待审批状态"),
//    FAIL(15, "审批失败状态"),
    SUCCESS(20, "审批成功状态"),
    ;

    private final Integer code;

    private final String desc;

    MemberApproveStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MemberApproveStatusEnum of(Integer code) {
        for (MemberApproveStatusEnum entityTypeEnum: values()) {
            if (entityTypeEnum.getCode().equals(code)) {
                return entityTypeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (MemberApproveStatusEnum typeEnum: values()) {
            if (typeEnum.getCode() > 0) {
                EnumInfo enumInfo = EnumInfo.newBuilder()
                        .setValue(typeEnum.getCode())
                        .setDesc(typeEnum.getDesc())
                        .build();
                res.add(enumInfo);
            }
        }
        return res;
    }
}
