package com.kuaishou.kwaishop.qa.risk.center.db.mapper.accuracy;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.QA_ACCURACY_SOURCE_NAME;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.accuracy.AffectEntranceInfoDO;

@Mapper
@DataSourceRouting(QA_ACCURACY_SOURCE_NAME)
public interface AffectEntranceInfoMapper extends BaseMapper<AffectEntranceInfoDO> {

}
