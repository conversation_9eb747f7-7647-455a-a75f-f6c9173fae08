package com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException.throwBizException;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.CENTER_ID_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.CENTER_TYPE_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_NOT_FOUND_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.OPERATOR_AUTH_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.TEAM_LEADER_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.TEAM_NAME_REPEAT_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.TEAM_NOT_FOUND_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.TEAM_TYPE_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringListConfigKey.adminUserNameList;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.EntityDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertFactory;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.TeamEntityBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityAbstract;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityService;
import com.kuaishou.kwaishop.qa.risk.center.task.kbus.model.CommonDataChangeEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@Slf4j
@Lazy
@Service
public class TeamEntityServiceImpl extends EntityAbstract implements EntityService {

    @Autowired
    private EntityDAO entityDAO;

    @Autowired
    private EntityConvertFactory entityConvertFactory;

    @Override
    public EntityDTO createEntity(EntityBO entityBO) {
        TeamEntityBO teamEntityBO = (TeamEntityBO) entityBO;
        // 把创建团队的权限给放开
//        if (!adminUserNameList.get().contains(entityBO.getOperator())) {
//            throwBizException(OPERATOR_AUTH_ERROR);
//        }
        if (StringUtils.isBlank(teamEntityBO.getLeader())) {
            throwBizException(TEAM_LEADER_EMPTY_ERROR);
        }
        centerCheck(teamEntityBO);
        // 是否重名
        EntityDO repeatDO = entityDAO.queryByName(getEntityType(), teamEntityBO.getEntityId(), teamEntityBO.getName());
        if (repeatDO != null) {
            throwBizException(TEAM_NAME_REPEAT_ERROR);
        }
        EntityConvertHandler handler = entityConvertFactory.getHandler(getEntityType());
        EntityDO createDO = handler.buildCreateDO(teamEntityBO);
        createDO = insert(createDO);
        return handler.buildDOToDTO(createDO);
    }

    @Override
    public void modifyEntity(EntityBO entityBO) {
        TeamEntityBO teamEntityBO = (TeamEntityBO) entityBO;
        if (!adminUserNameList.get().contains(entityBO.getOperator())) {
            throwBizException(OPERATOR_AUTH_ERROR);
        }
        if (StringUtils.isBlank(teamEntityBO.getLeader())) {
            throwBizException(TEAM_LEADER_EMPTY_ERROR);
        }
        // 团队是否存在
        EntityDO existDO = entityDAO.queryById(teamEntityBO.getId());
        if (existDO == null) {
            throwBizException(TEAM_NOT_FOUND_ERROR);
        }
        // 判断是否是团队类型
        if (!existDO.getEntityType().equals(getEntityType())) {
            throwBizException(TEAM_TYPE_ERROR);
        }
        centerCheck(teamEntityBO);
        // 是否重名
        EntityDO repeatDO = entityDAO.queryByName(getEntityType(), teamEntityBO.getEntityId(),
                teamEntityBO.getName(), teamEntityBO.getId());
        if (repeatDO != null) {
            throwBizException(TEAM_NAME_REPEAT_ERROR);
        }
        teamEntityBO.setVersion(existDO.getVersion());
        EntityConvertHandler handler = entityConvertFactory.getHandler(getEntityType());
        EntityDO modifyDO = handler.buildModifyDO(existDO, teamEntityBO);
        update(modifyDO);
    }

    @Override
    public Integer getEntityType() {
        return EntityTypeEnum.TEAM_TYPE.getCode();
    }

    @Override
    protected void deleteCheck(EntityDO existDO, String operator) {
        if (!adminUserNameList.get().contains(operator)) {
            throwBizException(OPERATOR_AUTH_ERROR);
        }
    }

    @Override
    public void handlerInsertEvent(CommonDataChangeEvent<EntityDO> changeEvent) {
    }

    @Override
    public void handlerUpdateEvent(CommonDataChangeEvent<EntityDO> changeEvent) {

    }

    private void centerCheck(TeamEntityBO teamEntityBO) {
        if (teamEntityBO.getEntityId() == null || teamEntityBO.getEntityId() <= 0) {
            throwBizException(CENTER_ID_EMPTY_ERROR);
        }
        // 质量中心是否存在
        EntityDO centerDO = entityDAO.queryById(teamEntityBO.getEntityId());
        if (centerDO == null) {
            throwBizException(ENTITY_NOT_FOUND_ERROR);
        }
        // 是否为质量中心类型
        if (!centerDO.getEntityType().equals(EntityTypeEnum.CENTER_TYPE.getCode())) {
            throwBizException(CENTER_TYPE_ERROR);
        }
    }
}
