package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressInterfaceBO;


public interface StressInterfaceService {
    PageBO<StressInterfaceDO> queryPageList(QueryStressScenarioBO queryBO);

    PageBO<StressInterfaceDO> queryPageListWithScenario(QueryStressScenarioBO queryBO, List<Long> scenarioIds);

    PageBO<StressInterfaceDO> queryPageListWithService(QueryStressScenarioBO queryBO, List<Long> serviceIds);

    StressInterfaceDO queryInterfaceDetail(Long id);

    Integer createOrUpdateInterface(StressInterfaceBO interfaceBO);

    Integer batchCreateInterface(List<StressInterfaceBO> interfaceBOs);
}