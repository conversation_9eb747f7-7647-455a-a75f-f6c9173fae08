package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-25
 */
public enum RentalStatusEnum {

    BORROWED(1, "已借用"),
    RETURNED(2, "已归还"),
    ;

    private final Integer code;

    private final String desc;

    RentalStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static RentalStatusEnum of(Integer code) {
        for (RentalStatusEnum typeEnum: values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
