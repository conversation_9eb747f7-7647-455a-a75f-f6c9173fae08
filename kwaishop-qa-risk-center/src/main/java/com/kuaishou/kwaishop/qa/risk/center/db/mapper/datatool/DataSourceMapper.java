package com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataSourceDo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-12
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface DataSourceMapper extends BaseMapper<DataSourceDo> {

    @Update("update data_source set creator_name = #{operator}, deleted = 1 where id = #{id}")
    int logicDeleted(@Param("operator") String operator, @Param("id") Long id);

    @Select("select * from data_source where  deleted=0 and name like CONCAT('%',#{name},'%') ")
    List<DataSourceDo> queryDataSourceLikeName(@Param("name") String name);

    @Select("select * from data_source  where deleted=0 and id = #{id} ")
    DataSourceDo queryDataSourceById(@Param("id") Long id);

    @Select("select * from data_source  where deleted=0 ")
    List<DataSourceDo> queryAllDataSource();

    @Insert("INSERT INTO data_source ( name, type, creator_name, description, content, create_time, update_time )  "
            + "VALUES  ( #{dataSourceDo.name}, #{dataSourceDo.type}, "
            + "#{dataSourceDo.creatorName}, #{dataSourceDo.description}, #{dataSourceDo.content}, #{dataSourceDo"
            + ".createTime},"
            + " #{dataSourceDo.updateTime} )")
    long insertDataSource(@Param("dataSourceDo") DataSourceDo dataSourceDo);

    @Update("update data_source set name = #{dataSourceDo.name}, description = #{dataSourceDo.description}, "
            + "type = #{dataSourceDo.type}, content = #{dataSourceDo.content}, "
            + "update_time = #{dataSourceDo.updateTime} "
            + "where id= #{dataSourceDo.id}")
    long updateDataSource(@Param("dataSourceDo") DataSourceDo dataSourceDo);


}
