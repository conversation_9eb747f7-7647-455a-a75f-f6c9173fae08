package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2025/4/3 15:53
 * @注释
 */
public enum ProcessKeyEnum {

    DEPOSIT_MERCHANT("deposit_test_merchant", "account_test_key"),
    DEPOSIT_DAREN("deposit_test_daren", "account_test_key");

    private final String tagValue;
    private final String processKey;

    ProcessKeyEnum(String tagValue, String processKey) {
        this.tagValue = tagValue;
        this.processKey = processKey;
    }

    public String getTagValue() {
        return tagValue;
    }

    public String getProcessKey() {
        return processKey;
    }

    public static ProcessKeyEnum of(String code) {
        for (ProcessKeyEnum processKeyEnum : values()) {
            if (processKeyEnum.getTagValue().equals(code)) {
                return processKeyEnum;
            }
        }
        return null;
    }
}
