package com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress;


import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioRecordDO;



@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StressScenarioRecordMapper extends BaseMapper<StressScenarioRecordDO> {

    @Select({"<script>",
            "SELECT * FROM stress_scenario_record",
            "WHERE 1=1",
            "<if test='id > 0'>and id = #{id}</if>",
            "</script>"})
    StressScenarioRecordDO selectScenarioRecordById(@Param("id") Long id);

    @Select({"<script>",
            "SELECT * FROM stress_scenario_record",
            "WHERE 1=1",
            "<if test='id > 0'>and id = #{id}</if>",
            "<if test='scenarioId > 0'>and scenario_id = #{scenarioId}</if>",
            "<if test='scenarioName != null'>and scenario_name like CONCAT('%',#{scenarioName},'%')</if>",
            "ORDER BY create_time DESC LIMIT #{startNo},#{pageSize}",
            "</script>"})
    List<StressScenarioRecordDO> queryStressScenarioRecords(@Param("id") Long id, @Param("scenarioName") String scenarioName,
            @Param("scenarioId") Long scenarioId, @Param("startNo") Integer startNo, @Param("pageSize")Integer pageSize);

    @Select({"<script>",
            "SELECT count(id) FROM stress_scenario_record",
            " WHERE 1=1",
            "<if test='id > 0'> and id = #{id}</if>",
            "<if test='scenarioId > 0'> and scenario_id = #{scenarioId}</if>",
            "<if test='scenarioName != null'> and scenario_name like CONCAT('%',#{scenarioName},'%')</if>"
            + "</script>"})
    Long countStressScenarioRecords(@Param("id") Long id, @Param("scenarioName") String scenarioName, @Param("scenarioId") Long scenarioId);
}
