package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo;

import java.util.Collection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-30
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EntityDataBO {

    private Long id;

    /**
     * 实体类型
     */
    private Integer entityType;

    /**
     * 实体id
     */
    private Long entityId;

    /**
     * 数据类型
     */
    private Integer dataType;

    /**
     * 数据详情
     */
    private String dataInfo;

    /**
     * 日期 20230329
     */
    private String dt;

    /**
     * 实体ids
     */
    private Collection<Long> entityIds;
}
