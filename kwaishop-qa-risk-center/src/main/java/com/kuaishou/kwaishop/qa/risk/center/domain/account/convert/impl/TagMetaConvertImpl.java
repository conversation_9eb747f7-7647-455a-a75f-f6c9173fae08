package com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagMetaDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.TagMetaConvert;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.TagBaseInfoDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 16:05
 * @注释
 */
@Slf4j
@Component
public class TagMetaConvertImpl implements TagMetaConvert {
    @Override
    public List<TagBaseInfoDTO> buildTagListInfoDTO(List<TagMetaDO> tagMetaDOs) {
        return tagMetaDOs.stream()
                .map(tagMetaDO -> TagBaseInfoDTO.newBuilder().setTagId(tagMetaDO.getTagId())
                        .setTagName(tagMetaDO.getTagName())
                        .setTagValue(tagMetaDO.getTagValue())
                        .setTagSubjectType(tagMetaDO.getTagSubjectType())
                        .setUserId(tagMetaDO.getUserId())
                        .setUserName(tagMetaDO.getUserName())
                        .setAccountType(tagMetaDO.getAccountType())
                        .setStatus(tagMetaDO.getStatus())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public TagMetaDO buildTagInfoDO(TagBaseInfoDTO tagBaseInfoDTO) {
        return null;
    }

    @Override
    public List<TagMetaDO> buildTagInfoDOs(List<TagBaseInfoDTO> tagBaseInfoDTOS) {

        return tagBaseInfoDTOS.stream()
                .map(tagBaseInfoDTO -> TagMetaDO.builder()
                        .tagId(tagBaseInfoDTO.getTagId())
                        .tagName(tagBaseInfoDTO.getTagName())
                        .tagValue(tagBaseInfoDTO.getTagValue())
                        .tagSubjectType(tagBaseInfoDTO.getTagSubjectType())
                        .userId(tagBaseInfoDTO.getUserId())
                        .userName(tagBaseInfoDTO.getUserName())
                        .accountType(tagBaseInfoDTO.getAccountType())
                        .riskLevel(tagBaseInfoDTO.getRiskLevel())
                        .onlineSystemName(tagBaseInfoDTO.getOnlineSystemName())
                        .status(tagBaseInfoDTO.getStatus())
                        .ext(tagBaseInfoDTO.getExt())
                        .createTime(System.currentTimeMillis())
                        .updateTime(System.currentTimeMillis())
                        .build()
                )
                .collect(Collectors.toList());

    }


}
