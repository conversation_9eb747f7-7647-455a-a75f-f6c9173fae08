package com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StressInterfaceMapper extends BaseMapper<StressInterfaceDO> {
    @Select("SELECT * FROM stress_interface WHERE id = #{interfaceId}")
    StressInterfaceDO getByInterfaceId(Long interfaceId);

    @Select("SELECT * FROM stress_interface WHERE interface_name = #{interfaceName} limit 0,1")
    StressInterfaceDO getByInterfaceName(String interfaceName);


    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    @Insert("insert into stress_interface(interface_name,interface_desc,service_id,service_name,status,create_time,update_time,deleted)"
            + " values(#{interfaceName},#{interfaceDesc},#{serviceId},#{serviceName},#{status},now(),now(),#{deleted})")
    Integer insertInterface(StressInterfaceDO stressInterfaceDO);


    @Select({"<script>", "SELECT * FROM stress_interface  WHERE 1=1 ",
            "and id in ",
            "<foreach item='item' collection='ids' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    List<StressInterfaceDO> getByInterfaceListByIds(List<Long> ids);
}
