package com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_DATA_DATA_TYPE_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_DATA_DT_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_DATA_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_DATA_INFO_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_ID_NOT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_TYPE_NOT_FOUND_ERROR;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.EntityDataDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDataDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityDataConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDataBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityDataTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityDataService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-30
 */
@Service
@Lazy
@Slf4j
public class EntityDataServiceImpl implements EntityDataService {

    @Autowired
    private EntityDataDAO entityDataDAO;

    @Autowired
    private EntityDataConvert entityDataConvert;

    @Override
    public void createEntityData(EntityDataBO entityDataBO) {
        if (entityDataBO == null) {
            throw new BizException(ENTITY_DATA_ERROR);
        }
        if (EntityTypeEnum.of(entityDataBO.getEntityType()) == null) {
            throw new BizException(ENTITY_TYPE_NOT_FOUND_ERROR);
        }
        if (EntityDataTypeEnum.of(entityDataBO.getDataType()) == null) {
            throw new BizException(ENTITY_DATA_DATA_TYPE_ERROR);
        }
        if (entityDataBO.getEntityId() == null || entityDataBO.getEntityId() <= 0) {
            throw new BizException(ENTITY_ID_NOT_EMPTY_ERROR);
        }
        if (StringUtils.isBlank(entityDataBO.getDt())) {
            throw new BizException(ENTITY_DATA_DT_ERROR);
        }
        if (StringUtils.isBlank(entityDataBO.getDataInfo())) {
            throw new BizException(ENTITY_DATA_INFO_ERROR);
        }
        entityDataDAO.insertOrUpdate(entityDataConvert.buildCreateDO(entityDataBO));
    }

    @Override
    public EntityDataDO queryEntityData(EntityDataBO entityDataBO) {
        if (entityDataBO == null) {
            throw new BizException(ENTITY_DATA_ERROR);
        }
        return entityDataDAO.queryEntityData(entityDataBO);
    }

    @Override
    public List<EntityDataDO> queryEntityDataList(EntityDataBO entityDataBO) {
        if (entityDataBO == null) {
            throw new BizException(ENTITY_DATA_ERROR);
        }
        return entityDataDAO.queryEntityDataList(entityDataBO);
    }

}
