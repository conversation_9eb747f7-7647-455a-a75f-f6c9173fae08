package com.kuaishou.kwaishop.qa.risk.center.db.mapper.account;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentAccountDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-25
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface RentAccountMapper extends BaseMapper<RentAccountDO> {

    @Insert("INSERT INTO rent_account(user_id, borrower, rental_time, duration, due_time, rental_status, team_id, center_id, "
            +
            "login_type, create_time, update_time, creator, modifier, password, account)"
            +
            "VALUES(#{userId}, #{borrower}, #{rentalTime}, "
            +
            "#{duration}, #{dueTime}, #{rentalStatus}, #{teamId}, #{centerId}, #{loginType}, #{createTime}, #{updateTime},"
            +
            "#{creator}, #{modifier}, #{password}, #{account})")
    int insert(RentAccountDO rentAccountDO);

    //TODO：鉴权，谁来操作借还
    @Update("update rent_account set rental_status = #{rental_status}, due_time = #{due_time}, modifier = #{modifier}, "
            +
            "update_time = #{update_time} where id = #{id}")
    int returnAccount(@Param("rental_status") Integer rentalStatus, @Param("due_time") Long dueTime, @Param("modifier") String modifier,
                      @Param("update_time") Long updateTime, @Param("id") Long id);

    @Select("select * from rent_account where borrower = #{borrower}")
    List<RentAccountDO> queryListByOperator(String borrower);

    @Select("select * from rent_account limit 1000")
    List<RentAccountDO> queryListByAllOperator();
}
