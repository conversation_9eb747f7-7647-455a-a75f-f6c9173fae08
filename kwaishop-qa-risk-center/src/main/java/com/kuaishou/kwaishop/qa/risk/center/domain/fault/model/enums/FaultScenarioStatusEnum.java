package com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums;

public enum FaultScenarioStatusEnum {

    UNKNOWN(0, "未知类型"),
    UNDO(5, "未演练"),
    PASS(10, "通过"),
    NOTPASS(15, "未通过"),
    CHANGE(20, "待评估"),
    NEEDDO(25, "待演练");

    private final Integer code;

    private final String desc;

    FaultScenarioStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static FaultScenarioStatusEnum of(Integer code) {
        for (FaultScenarioStatusEnum entityTypeEnum : values()) {
            if (entityTypeEnum.getCode().equals(code) && entityTypeEnum.getCode() > 0) {
                return entityTypeEnum;
            }
        }
        return null;
    }

    public static FaultScenarioStatusEnum of(String desc) {
        for (FaultScenarioStatusEnum entityTypeEnum : values()) {
            if (entityTypeEnum.getDesc().equals(desc)) {
                return entityTypeEnum;
            }
        }
        return FaultScenarioStatusEnum.UNKNOWN;
    }

}
