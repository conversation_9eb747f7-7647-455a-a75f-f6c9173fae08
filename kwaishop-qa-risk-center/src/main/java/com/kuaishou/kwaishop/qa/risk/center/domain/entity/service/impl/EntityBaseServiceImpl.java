package com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException.throwBizException;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_ID_NOT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_NOT_FOUND_ERROR;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.EntityDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertFactory;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityBaseBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityAbstract;
import com.kuaishou.kwaishop.qa.risk.center.task.kbus.model.CommonDataChangeEvent;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@Slf4j
@Lazy
@Service
public class EntityBaseServiceImpl extends EntityAbstract {

    @Autowired
    private EntityDAO entityDAO;

    @Autowired
    private EntityConvertFactory entityConvertFactory;

    @Override
    public EntityDTO createEntity(EntityBO entityBO) {
        EntityBaseBO entityBaseBO = (EntityBaseBO) entityBO;
        EntityConvertHandler handler = entityConvertFactory.getHandler(entityBaseBO.getEntityType());
        EntityDO entityDO = handler.buildCreateDO(entityBaseBO);
        entityDO = insert(entityDO);
        return handler.buildDOToDTO(entityDO);
    }

    @Override
    public void modifyEntity(EntityBO entityBO) {
        EntityBaseBO entityBaseBO = (EntityBaseBO) entityBO;
        if (entityBaseBO.getId() == null || entityBaseBO.getId() <= 0) {
            throwBizException(ENTITY_ID_NOT_EMPTY_ERROR);
        }
        EntityDO existDO = entityDAO.queryById(entityBaseBO.getId());
        if (existDO == null) {
            throwBizException(ENTITY_NOT_FOUND_ERROR);
        }
        entityBaseBO.setVersion(existDO.getVersion());
        EntityConvertHandler handler = entityConvertFactory.getHandler(entityBaseBO.getEntityType());
        EntityDO entityDO = handler.buildModifyDO(existDO, entityBaseBO);
        update(entityDO);
    }

    @Override
    public Integer getEntityType() {
        return EntityTypeEnum.DEFAULT.getCode();
    }

    @Override
    protected void deleteCheck(EntityDO existDO, String operator) {

    }

    @Override
    public void handlerInsertEvent(CommonDataChangeEvent<EntityDO> changeEvent) {

    }

    @Override
    public void handlerUpdateEvent(CommonDataChangeEvent<EntityDO> changeEvent) {

    }
}
