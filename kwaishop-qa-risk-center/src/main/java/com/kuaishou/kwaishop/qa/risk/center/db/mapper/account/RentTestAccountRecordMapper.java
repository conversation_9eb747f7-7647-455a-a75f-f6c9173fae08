package com.kuaishou.kwaishop.qa.risk.center.db.mapper.account;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentTestAccountRecordDO;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 16:45
 * @注释
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface RentTestAccountRecordMapper extends BaseMapper<RentTestAccountRecordDO> {


    @Update("update rent_test_account_record set rental_status = #{rental_status},  modifier = #{modifier}, "
            +
            " update_time = #{update_time} where id = #{id}")
    int returnAccount(@Param("rental_status") Integer rentalStatus, @Param("modifier") String modifier,
                      @Param("update_time") Long updateTime, @Param("id") Long id);

    @Select("select * from rent_test_account_record where borrower = #{borrower}")
    List<RentTestAccountRecordDO> queryListByOperator(String borrower);

    @Select("select * from rent_test_account_record where kwai_id = #{kwai_id}")
    List<RentTestAccountRecordDO> queryByUid(@Param("kwai_id")Long uid);

    @Insert("INSERT INTO rent_test_account_record (test_account_id,kwai_id, b_id,borrower, rental_time, duration, due_time, rental_status, "
            +
            "login_type, create_time, update_time, creator, modifier, proxy_password, account) "
            +
            "VALUES (#{testAccountId}, #{kwaiId}, #{bId}, #{borrower}, #{rentalTime}, "
            +
            "#{duration}, #{dueTime}, 1, #{loginType}, #{createTime}, #{updateTime}, "
            +
            "#{creator}, #{modifier}, #{proxyPassword}, #{account})")
    int insert(RentTestAccountRecordDO rentTestAccountRecordDO);

    @Select("select * from rent_test_account_record where kwai_id = #{kwai_id} and rental_status = #{rental_status}")
    List<RentTestAccountRecordDO> queryList(@Param("kwai_id") Long userId, @Param("rental_status") Integer rentalStatus);

    @Select("select * from rent_test_account_record where rental_status = #{rental_status}")
    List<RentTestAccountRecordDO> queryAllRentAccountList(@Param("rental_status") Integer rentalStatus);

}
