package com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioCaseDO;


/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-03-15
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface ScenarioCaseMapper extends BaseMapper<ScenarioCaseDO>  {
    @Update("update data_scenario_case set modifier = #{operator}, deleted = 1 where id = #{id}")
    int logicDeleted(@Param("operator") String operator, @Param("id") Long id);

    @Select("select * from data_scenario_case  where deleted=0 and id = #{id} ")
    ScenarioCaseDO queryScenarioCaseById(@Param("id") Long id);

    @Select("select * from data_scenario_case  where deleted=0 ")
    List<ScenarioCaseDO> queryScenarioCase();

    @Select("select id from data_scenario_case  where deleted=0 and scenario_id = #{scenarioId} and case_id = #{caseId}")
    Long queryIdByScenarioIdCaseId(@Param("scenarioId") Long scenarioId, @Param("caseId") Long caseId);

    @Select("select case_id from data_scenario_case  where deleted=0 and scenario_id = #{scenarioId}")
    List<Long> queryCaseIdsByScenarioId(@Param("scenarioId") Long scenarioId);
}
