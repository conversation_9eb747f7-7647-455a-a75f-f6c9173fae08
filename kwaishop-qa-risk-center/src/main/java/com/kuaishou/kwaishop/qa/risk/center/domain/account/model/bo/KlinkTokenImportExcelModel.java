package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.excel.annotation.ExcelProperty;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.annotation.ExcelNotNull;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.annotation.ExcelStringFormat;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.ValidResultBO;

import lombok.Data;


@Data
public class KlinkTokenImportExcelModel extends BaseExcelModel {

    @ExcelNotNull
    @ExcelProperty("userId")
    private Long userId;

    @ExcelNotNull
    @ExcelStringFormat
    @ExcelProperty("kuaishou.shop.im_st")
    private String st;

    @ExcelNotNull
    @ExcelProperty("ssecurity")
    private String ssecurity;

    @ExcelNotNull
    @ExcelStringFormat
    @ExcelProperty("tokenClientSalt")
    private String tokenClientSalt;

    @Override
    public ValidResultBO customValid() {
        ValidResultBO res = new ValidResultBO();
        res.setResult(Boolean.TRUE);
        res.setFailMsg(StringUtils.EMPTY);
        return res;
    }

}
