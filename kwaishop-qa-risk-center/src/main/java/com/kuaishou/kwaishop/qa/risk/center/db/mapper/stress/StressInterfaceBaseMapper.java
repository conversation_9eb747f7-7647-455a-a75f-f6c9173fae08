package com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceBaseDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StressInterfaceBaseMapper extends BaseMapper<StressInterfaceBaseDO> {

    @Select({"<script>"
            + "SELECT * FROM stress_interface_base "
            + " WHERE 1=1 "
            + "<if test='interfaceId > 0'>and interface_id = #{interfaceId}</if>"
            + "<if test='interfaceName != null'>and interface_name like CONCAT('%',#{interfaceName},'%')</if>"
            + "ORDER BY create_time DESC LIMIT #{startNo},#{pageSize} "
            + "</script>"})
    List<StressInterfaceBaseDO> queryStressInterfaceBases(@Param("interfaceId") Long interfaceId, @Param("interfaceName") String interfaceName,
            @Param("startNo") Integer startNo, @Param("pageSize")Integer pageSize);

    @Select({"<script>"
            + "SELECT count(id) FROM stress_interface_base "
            + " WHERE 1=1 "
            + "<if test='interfaceId > 0'>and interface_id = #{interfaceId}</if>"
            + "<if test='interfaceName != null'> and interface_name like CONCAT('%',#{interfaceName},'%')</if>"
            + "</script>"})
    Long countStressInterfaceBases(@Param("interfaceId") Long interfaceId, @Param("interfaceName") String interfaceName);

    @Select({"<script>"
            + "SELECT * FROM stress_interface_base "
            + " WHERE "
            + "interface_id = #{interfaceId} limit 0,1"
            + "</script>"})
    StressInterfaceBaseDO queryStressInterfaceBaseByInterfaceId(@Param("interfaceId") Long interfaceId);
}
