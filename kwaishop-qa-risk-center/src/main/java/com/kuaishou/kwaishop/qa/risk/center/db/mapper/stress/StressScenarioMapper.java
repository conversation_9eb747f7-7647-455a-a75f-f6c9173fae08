package com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Many;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StressScenarioMapper extends BaseMapper<StressScenarioDO> {
    @Select({"<script>"
            + "SELECT * FROM stress_scenario "
            + " WHERE 1=1 "
            + "<if test='scenarioId > 0'>and scenario_id = #{scenarioId}</if>"
            + "<if test='name != null'>and name like CONCAT('%',#{name},'%')</if>"
            + "ORDER BY create_time DESC LIMIT #{startNo},#{pageSize} "
            + "</script>"})
    List<StressScenarioDO> queryStressScenarios(@Param("scenarioId") Long scenarioId, @Param("name") String name,
            @Param("startNo") Integer startNo, @Param("pageSize")Integer pageSize);


    @Select({"<script>"
            + "SELECT * FROM stress_scenario Where out_id = #{outId} and deleted = 0"
            + " ORDER BY create_time DESC LIMIT 0,1 "
            + "</script>"})
    StressScenarioDO selectByOutId(@Param("outId") String outId);

    @Options(useGeneratedKeys = true, keyProperty = "id", keyColumn = "id")
    @Insert("insert into stress_scenario(name,status,out_url,out_id,owner_id,deleted,create_time,update_time)"
            + " values(#{name},#{status},#{outUrl},#{outId},#{ownerId},#{deleted},now(),now())")
    Integer insertScenario(StressScenarioDO stressScenarioDO);



    @Select({"<script>"
            + "SELECT ss.*, sir.interface_id FROM stress_scenario ss "
            + " LEFT JOIN stress_scenario_interface_rel sir ON ss.id = sir.scenario_id"
            + " LEFT JOIN stress_interface si ON  si.id = sir.interface_id "
            + "WHERE 1=1 "
            + "<if test='scenarioId > 0'>and scenario_id = #{scenarioId}</if> "
            + "<if test='name != null'>and name like CONCAT('%',#{name},'%')</if>"
            + "ORDER BY create_time DESC LIMIT #{startNo},#{pageSize} "
            + "</script>"})
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "name", column = "name"),
            @Result(property = "ownerId", column = "owner_id"),
            @Result(property = "stressInterfaces", column = "interface_id",
                    many = @Many(select = "com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress.StressInterfaceMapper.getByInterfaceId"))
    })
    List<StressScenarioDO> queryStressScenariosWithInterface(@Param("scenarioId") Long scenarioId, @Param("name") String name,
            @Param("startNo") Integer startNo, @Param("pageSize")Integer pageSize);

    @Select({"<script>"
            + "SELECT count(id) FROM stress_scenario "
            + " WHERE 1=1 "
            + "<if test='scenarioId > 0'>and scenario_id = #{scenarioId}</if>"
            + "<if test='name != null'> and name like CONCAT('%',#{name},'%')</if>"
            + "</script>"})
    Long countStressScenarios(@Param("scenarioId") Long scenarioId, @Param("name") String name);
}
