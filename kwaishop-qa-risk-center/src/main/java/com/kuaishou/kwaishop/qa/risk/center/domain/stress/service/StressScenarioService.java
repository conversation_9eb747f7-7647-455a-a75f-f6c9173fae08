package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressScenarioBO;

public interface StressScenarioService {
    PageBO<StressScenarioDO> queryPageList(QueryStressScenarioBO queryBO);
    StressScenarioDO getScenario(StressScenarioBO scenarioBO);
    PageBO<StressScenarioDO> queryPageListWithInterface(QueryStressScenarioBO queryBO);
    Boolean createScenarioFromImport(String importStr);
}
