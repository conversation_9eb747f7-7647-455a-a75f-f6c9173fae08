package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.AccountRentalDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ApplyAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ExtendRentRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.PageRentAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageRentRecordsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryRentRecordsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.RentAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ReturnAccountRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-26
 */
public interface RentAccountBizService {

    //租借账户
    void rentAccount(RentAccountRequest request);

    //申领
    void rentAccount(ApplyAccountRequest request, UserAccountDO accountDO);

    //归还账户
    void returnAccount(ReturnAccountRequest request);

    void autoReturnAccount(ReturnAccountRequest request);

    //租借记录查询
    List<AccountRentalDTO> queryRentRecords(QueryRentRecordsRequest request);

    //翻页查询租借记录
    PageRentAccountDTO queryPageRentRecords(QueryPageRentRecordsRequest request, boolean isDetail);


    void extendRent(ExtendRentRequest request);

    void autoReturn();

    List<String> queryListByOperator(RentAccountBO build);
}
