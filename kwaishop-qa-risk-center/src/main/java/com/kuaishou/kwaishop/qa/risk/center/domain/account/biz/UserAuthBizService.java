package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAuthBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateAllUserInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateUserAuthRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-24
 */
public interface UserAuthBizService {

    void updateAuthInfo(UpdateUserAuthRequest updateUserAuthRequest);

    void updateAutoAllAuth();

    void insertAuthInfo(Long userId);

    List<UserAuthDO> queryUserAuthRecords(UserAuthBO userAuthBO);

    void updateUserAllAuthInfo(UpdateAllUserInfoRequest request);
}
