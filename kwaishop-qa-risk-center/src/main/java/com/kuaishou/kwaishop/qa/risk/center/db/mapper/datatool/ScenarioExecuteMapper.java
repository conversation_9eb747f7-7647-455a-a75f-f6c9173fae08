package com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.ScenarioExecuteDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-03-15
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface ScenarioExecuteMapper extends BaseMapper<ScenarioExecuteDO> {

    @Update("update data_scenario_exe set creator_name = #{operator}, deleted = 1 where id = #{id}")
    int logicDeleted(@Param("operator") String operator, @Param("id") Long id);


    @Update("<script>"
            + "update data_scenario_exe "
            + "set "
            + "status =  #{status} "
            + "where "
            + "id = #{id}"
            + "</script>")
    void updateStatus(@Param("id") Long id, @Param("status") Integer status);

    @Select("select * from data_scenario_exe where  deleted=0 and name like CONCAT('%',#{name},'%') ")
    List<ScenarioExecuteDO> queryScenarioExecuteLikeName(@Param("name") String name);



    @Select("select * from data_scenario_exe  where deleted=0 and id = #{id} ")
    ScenarioExecuteDO queryScenarioExecuteById(@Param("id") Long id);

    @Select("select * from data_scenario_exe  where deleted=0 and case_id = #{id} ")
    List<ScenarioExecuteDO> queryScenarioExecuteByCaseId(@Param("id") Long id);

    @Select("select * from data_scenario_exe  where deleted=0 ")
    List<ScenarioExecuteDO> queryScenarioExecute();

}
