package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
public enum UserLoginTypeEnum {

    UNKNOWN(0, "未知登录类型"),
    CITY_86_LOGIN(1, "+86手机登录类型"),
    CITY_1264_LOGIN(2, "+1264手机登录类型"),
    EMAIL_LOGIN(3, "邮箱登录类型"),
    ;

    private final Integer code;
    private final String desc;

    UserLoginTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static UserLoginTypeEnum of(Integer code) {
        for (UserLoginTypeEnum typeEnum: values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (UserLoginTypeEnum typeEnum: values()) {
            EnumInfo enumInfo = EnumInfo.newBuilder()
                    .setValue(typeEnum.getCode())
                    .setDesc(typeEnum.getDesc())
                    .build();
            res.add(enumInfo);
        }
        return res;
    }
}
