package com.kuaishou.kwaishop.qa.risk.center.domain.entity.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityPageDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.task.kbus.model.CommonDataChangeEvent;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
public interface EntityService {

    EntityDTO createEntity(EntityBO entityBO);

    void modifyEntity(EntityBO entityBO);

    void logicDelete(String operator, Long id);

    void logicDelete(String operator, Integer entityType, Long entityId);

    List<EntityDTO> queryEntityList(EntityQueryBO queryBO);

    EntityPageDTO queryEntityPageList(EntityQueryBO queryBO);

    Integer getEntityType();

    void handlerInsertEvent(CommonDataChangeEvent<EntityDO> changeEvent);

    void handlerUpdateEvent(CommonDataChangeEvent<EntityDO> changeEvent);

    List<EntityDO> queryByEntityType();

    Map<Long, EntityDO> queryGroupByEntityType();

    List<EntityDO> queryByEntityId(Long entityId);

    List<EntityDO> queryByEntityIds(Collection<Long> entityIds);

    Map<Long, List<EntityDO>> queryGroupByEntityId(Collection<Long> entityIds);
}
