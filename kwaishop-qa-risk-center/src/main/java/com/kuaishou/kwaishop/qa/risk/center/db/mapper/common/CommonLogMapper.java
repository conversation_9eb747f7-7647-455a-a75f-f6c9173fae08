package com.kuaishou.kwaishop.qa.risk.center.db.mapper.common;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.common.CommonLogDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-24
 */

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface CommonLogMapper extends BaseMapper<CommonLogDO> {


    @Insert("<script>"
            + "INSERT INTO `common_log`(`id`, `date_type`, `statistics_type`, `log_type`, `dt`, `name`, `total`,"
            + "`create_time`, `update_time`, `creator`, `modifier`)"
            + "VALUES"
            + "<foreach collection='logList' index='index' item='log' separator=','>"
            + "(#{log.id}, #{log.dateType}, #{log.statisticsType}, #{log.logType}, #{log.dt}, #{log.name}, #{log.total},"
            + "#{log.createTime}, #{log.updateTime}, #{log.creator}, #{log.modifier})"
            + "</foreach>"
            + "ON DUPLICATE KEY UPDATE "
            + "total=VALUES(total), update_time=VALUES(update_time), version = version+1, modifier=VALUES(modifier)"
            + "</script>")
    long batchInsertOrUpdate(@Param("logList") List<CommonLogDO> logDOList);


    @Insert("<script>"
            + "INSERT INTO `common_log`(`id`, `date_type`, `statistics_type`, `log_type`, `dt`, `name`, `total`,"
            + "`create_time`, `update_time`, `creator`, `modifier`)"
            + "VALUES"
            + "(#{log.id}, #{log.dateType}, #{log.statisticsType}, #{log.logType}, #{log.dt}, #{log.name}, #{log.total},"
            + "#{log.createTime}, #{log.updateTime}, #{log.creator}, #{log.modifier})"
            + "ON DUPLICATE KEY UPDATE "
            + "total = #{log.total}, update_time=#{log.updateTime}, version = version+1, modifier=#{log.modifier}"
            + "</script>")
    long insertOrUpdate(@Param("log") CommonLogDO logDO);
}
