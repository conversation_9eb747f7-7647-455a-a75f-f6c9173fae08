package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EntityPageDTO {

    private Integer pageNo;

    private Integer pageSize;

    private Long total;

    private List<EntityDTO> data;
}
