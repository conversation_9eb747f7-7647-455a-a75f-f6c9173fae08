package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ApproveEntityDTO extends EntityDTO {

    private Long teamId;
}
