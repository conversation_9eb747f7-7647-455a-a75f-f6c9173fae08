package com.kuaishou.kwaishop.qa.risk.center.domain.entity.service;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_TYPE_NOT_FOUND_ERROR;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@Slf4j
@Lazy
@Service
public class EntityServiceFactory {

    @Autowired
    private List<EntityService> entityServices;

    private Map<Integer, EntityService> entityServiceMap;

    @PostConstruct
    private void init() {
        entityServiceMap = new HashMap<>();
        entityServices.forEach(p -> entityServiceMap.put(p.getEntityType(), p));
    }

    public EntityService getEntityService(Integer entityType) {
        if (EntityTypeEnum.of(entityType) != null) {
            if (entityServiceMap.containsKey(entityType)) {
                return entityServiceMap.get(entityType);
            }
            return entityServiceMap.get(EntityTypeEnum.DEFAULT.getCode());
        }
        throw new BizException(ENTITY_TYPE_NOT_FOUND_ERROR);

    }

    public EntityService getDefaultService() {
        return entityServiceMap.get(EntityTypeEnum.DEFAULT.getCode());
    }

}
