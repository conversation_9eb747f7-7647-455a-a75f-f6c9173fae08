package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.infra.passport.internal.sdk.service.PassportUserService;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.mapper.account.UserAccountMapper;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.CheckAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAccountService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.BatchCheckAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.CheckAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.service.impl.TestDpmServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/10/16 15:30
 * @注释
 */
@Lazy
@Service
@Slf4j
public class CheckAccountBizServiceImpl implements CheckAccountBizService {

    private final int from = 127;
    private final int to = 127;

    @Autowired
    private UserAccountService userAccountService;

    @Autowired
    private TestDpmServiceImpl testDpmService;

    @Autowired
    private UserAccountMapper userAccountMapper;

    @Autowired
    private PassportUserService passportUserService;


    @Override
    public boolean checkAccount(CheckAccountRequest req) {

        return true;
    }

    @Override
    public boolean batchCheckAccount(BatchCheckAccountRequest batchCheckAccountRequest) {
        List<UserAccountDO> userAccountDOS = userAccountService.queryBusinessAccountByRule(from, to);

        return false;
    }

    @Override
    public void checkAndDeleteAccount() {
        List<UserAccountDO> list = userAccountMapper.selectAllAccountsWithCreator().stream()
                .filter(userAccount ->
                        !userAccount.getCreator().equals("chenshengrui") //最初导入的名字暂不调整
                                && !userAccount.getCreator().equals("pengshengpu") //最初导入的名字暂不调整
                                && !testDpmService.isOnDuty(userAccount.getCreator())
                                && passportUserService.isKsTestAccount(userAccount.getUserId()).isSuccess()
                                && !passportUserService.isKsTestAccount(userAccount.getUserId()).getResult()
                )
                .collect(Collectors.toList());
        log.info("checkAccount:{}", ObjectMapperUtils.toJSON(list));
        //删除掉离职且非测试账号的账号
//        list.forEach(userAccount -> {
//            userAccountMapper.deleteById(userAccount.getId());
//        });

    }


}
