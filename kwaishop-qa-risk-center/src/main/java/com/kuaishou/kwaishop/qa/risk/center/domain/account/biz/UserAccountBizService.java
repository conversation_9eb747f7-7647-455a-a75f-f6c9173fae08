package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.AccountManagementRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ApplyAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetAccountTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountExcelRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportAccountTokenRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.PageUserAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateAllUserInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.customer.ImportKlinkTokenRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
public interface UserAccountBizService {

    List<UserAccountDTO> queryUserAccountList(QueryUserAccountRequest request);

    PageUserAccountDTO queryPageUserAccountList(QueryPageUserAccountRequest request);

    List<Long> queryTestAccountList(GetTestAccountRequest request);

    void importAccountExcel(ImportAccountExcelRequest request);

    void importUserAccount(ImportUserAccountRequest request);

    String getAccountToken(GetAccountTokenRequest request);

    void updateAllUserInfo(UpdateAllUserInfoRequest request);

    void importAccountToken(ImportAccountTokenRequest request);

    void accountManagement(AccountManagementRequest request);

    void importKlinkToken(ImportKlinkTokenRequest request);

    void applyAccount(ApplyAccountRequest request) throws Exception;

}
