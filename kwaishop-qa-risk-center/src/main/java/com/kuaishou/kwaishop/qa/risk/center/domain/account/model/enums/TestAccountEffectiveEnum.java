package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2025/3/4 15:27
 * @注释
 */
public enum TestAccountEffectiveEnum {
    USED(0, "有效账号"),
    DELETED(1, "账号已删除"),
    INVALID(2, "账号无效");


    private final Integer code;
    private final String desc;

    TestAccountEffectiveEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static TestAccountEffectiveEnum of(Integer code) {
        for (TestAccountEffectiveEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
