package com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_TYPE_NOT_FOUND_ERROR;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@Slf4j
@Lazy
@Service
public class EntityConvertFactory {

    @Autowired
    private List<EntityConvertHandler> entityConvertHandlers;

    private Map<Integer, EntityConvertHandler> handlerMap;

    @PostConstruct
    private void init() {
        handlerMap = new HashMap<>();
        entityConvertHandlers.forEach(p -> handlerMap.put(p.getEntityType(), p));
    }

    public EntityConvertHandler getHandler(Integer entityType) {
        if (EntityTypeEnum.of(entityType) != null) {
            if (handlerMap.containsKey(entityType)) {
                return handlerMap.get(entityType);
            }
            return handlerMap.get(EntityTypeEnum.DEFAULT.getCode());
        }
        throw new BizException(ENTITY_TYPE_NOT_FOUND_ERROR);
    }

}
