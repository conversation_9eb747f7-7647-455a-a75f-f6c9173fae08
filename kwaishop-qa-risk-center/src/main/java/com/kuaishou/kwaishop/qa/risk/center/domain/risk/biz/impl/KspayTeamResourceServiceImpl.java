package com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCESSTOKEN_GET_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCESSTOKEN_GET_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCURACY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCURACY_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCURACY_PAY_FINANCE_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.FEATUREID_GET_BY_BIZ_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.FEATUREID_GET_BY_BIZ_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.FEATUREID_GET_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.FEATUREID_GET_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.FEATURE_DETAIL_GET_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.FEATURE_DETAIL_GET_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.GETBASEINFO_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.GETBASEINFO_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.QUERY_FEATURE_INFO_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.QUERY_FEATURE_INFO_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.QUERY_TASK_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.QUERY_TASK_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.SEND_KIM_MESSAGE_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.SEND_KIM_MESSAGE_INFO_NULL;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringConfigKey.kspayAccuracyUrl;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringConfigKey.kspayKimBotSendMessageUrl;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringConfigKey.kspayOpenApiUrl;

import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kspay.util.ProtoPrinterUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskJsonMapConfigKey;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundsRiskFeatureDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.customer.config.OpenApiAppConfig;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.biz.KspayTeamResourceService;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.bo.OpenApiAccessTokenBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.bo.OpenApiTaskInfBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.risk.model.bo.OpenApiTaskQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.AccuracyData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.BranchInfoData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.BranchInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.FeatureInfo;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.FeatureInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.FinanceMethod;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.KdevFeatureDetailData;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.KdevFeatureDetailResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.KdevFeatureInfoResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.KdevFeatureList;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.KdevFeatureListResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.KdevResourceResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.QueryTeamTaskRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.team.TaskInfo;
import com.kuaishou.kwaishop.qa.risk.center.utils.builder.MapBuilder;
import com.kuaishou.kwaishop.qa.risk.center.utils.http.HttpUtils;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-27
 */
@Service
@Lazy
@Slf4j
public class KspayTeamResourceServiceImpl implements KspayTeamResourceService {
    private static Integer connectTimeOut = 10000;  // 60000
    private static Integer readTimeOut = 10000;
    private static Integer writeTimeOut = 10000;
    private static Integer connectTimeOutShort = 6000;
    private static Integer readTimeOutShort = 6000;
    private static Integer writeTimeOutShort = 6000;
    private static Integer connectTimeOutLong = 15000;
    private static Integer readTimeOutLong = 15000;
    private static Integer writeTimeOutLong = 15000;
    private final HttpUtils httpUtils = new HttpUtils(connectTimeOut, readTimeOut, writeTimeOut);
    private final HttpUtils httpUtilsShortTime = new HttpUtils(connectTimeOutShort, readTimeOutShort, writeTimeOutShort);
    private final HttpUtils httpUtilsLongTime = new HttpUtils(connectTimeOutLong, readTimeOutLong, writeTimeOutLong);
    private static String getAccessTokenPath = "/token/get?";
    private static String openApiAppId = "6429";
    private static String openApiAppId1 = "9354";
    // 这个看起来用不上，准备使用已有的方法【这里代码应该还有问题】
    @Override
    public Boolean sendKimBotMessage(List<FundsRiskFeatureDO> fundsRiskFeatureDOS, String riskPlatFormUrl, String webHook) {
        String url = kspayKimBotSendMessageUrl.get() + "/api/robot/send?key=" + webHook;
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        com.kuaishou.intown.json.JSONObject markdown = new com.kuaishou.intown.json.JSONObject();
        StringBuilder markdownTitle =
                new StringBuilder(String.format("## <font color=\"red\">【已上线资损未打标需求】【请打标】【%s】</font>\n", "请相关同学关注"));
        StringBuilder markdownBody = new StringBuilder("| 任务id | 报警内容 |\n"
                + "|---|---| \n"
        );
        markdownTitle.append(markdownBody);
        String markdownText = markdownTitle.append(riskPlatFormUrl).toString();
        markdown.put("content", markdownText);
        Map<String, Object> value = MapBuilder.<String, Object>newMapBuilder()
                .put("markdown", markdown)
                .put("msgType", "markdown")
                .build();
        try {
            Response response = httpUtils.postMapObject(url, headers, value);
            if (!response.isSuccessful()) {
                throw new BizException(SEND_KIM_MESSAGE_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(SEND_KIM_MESSAGE_INFO_NULL);
            }
            String responseBody = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] SendKimBotMessage value: {}", value);
            log.info("[KspayTeamResourceServiceImpl] SendKimBotMessage res: {}", responseBody);
            return true;
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] queryTeamTask error,", e);
            return false;
        }
    }
    @Override
    public List<TaskInfo> queryTeamTask(QueryTeamTaskRequest request) {
        String url = kspayOpenApiUrl.get() + "/pm/api/no-ba/external/task/query";
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + getAccessToken());
        try {
            Response response = httpUtils.post(url, headers, toJSON(request));
            if (!response.isSuccessful()) {
                throw new BizException(QUERY_TASK_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(QUERY_TASK_NULL);
            }
            String responseBody = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] queryTeamTask request: {}", request);
            log.info("[KspayTeamResourceServiceImpl] queryTeamTask res: {}", responseBody);
            List<TaskInfo> taskInfos = new ArrayList<>();
            OpenApiTaskQueryBO tasks = ObjectMapperUtils.fromJSON(responseBody, OpenApiTaskQueryBO.class);
            for (OpenApiTaskInfBO task : tasks.getResult()) {
                TaskInfo taskInfo = TaskInfo.newBuilder()
                        .setTaskId(task.getTaskId())
                        .setTitle(task.getTitle())
                        .setStatus(task.getStatus())
                        .setAssignee(task.getAssignee())
                        .setCreator(task.getCreator())
                        .setCreateTime(task.getCreateTime())
                        .setProjectId(task.getProjectId())
                        .setSection(task.getSection())
                        .setSectionId(task.getSectionId())
                        .setProjectName(task.getProjectName())
                        .build();
                taskInfos.add(taskInfo);
            }
            return taskInfos;
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] queryTeamTask error,", e);
            return new ArrayList<>();
        }
    }
    @Override
    public List<FeatureInfo> queryFeatureInfo(FeatureInfoRequest request) {
        int featureId = getfeatureId(request.getTeamId());
        String url = kspayOpenApiUrl.get() + "/api/kdev/openapi/feature/info?id=" + featureId;
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + getAccessToken());
        try {
            Response response = httpUtils.get(url, headers);
            if (!response.isSuccessful()) {
                throw new BizException(QUERY_FEATURE_INFO_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(QUERY_FEATURE_INFO_NULL);
            }
            String responseBody = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] queryFeatureInfo request: {}", request);
            log.info("[KspayTeamResourceServiceImpl] queryFeatureInfo res: {}", responseBody);
            List<FeatureInfo> featureInfos = new ArrayList<>();
            KdevFeatureInfoResponse
                    kdevFeatureInfoResponse = ObjectMapperUtils.fromJSON(responseBody, KdevFeatureInfoResponse.class);
            FeatureInfo featureInfo = FeatureInfo.newBuilder()
                    .setFeatureId(String.valueOf(kdevFeatureInfoResponse.getData().getId()))
                    .setTitle(kdevFeatureInfoResponse.getData().getTitle())
                    .setStatus(kdevFeatureInfoResponse.getData().getStatus())
                    .setTeamId(kdevFeatureInfoResponse.getData().getTeamId())
                    .build();
            featureInfos.add(featureInfo);
            return featureInfos;
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] queryTeamTask error,", e);
            return new ArrayList<>();
        }
    }
    private String getAccessToken() {
        Map<String, OpenApiAppConfig> openApiAppConfig = QaRiskJsonMapConfigKey.openApiAppKey.getMap();
        String appKey = openApiAppConfig.get(openApiAppId).getAppKey();
        String secretKey = openApiAppConfig.get(openApiAppId).getSecretKey();
        String url = kspayOpenApiUrl.get() + getAccessTokenPath + "appKey=" + appKey + "&secretKey=" + secretKey;
        try {
            Response response = httpUtils.get(url, null);
            if (!response.isSuccessful()) {
                throw new BizException(ACCESSTOKEN_GET_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(ACCESSTOKEN_GET_NULL);
            }
            String res = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] getAccessToken res: {}", res);
            return ObjectMapperUtils.fromJSON(res, OpenApiAccessTokenBO.class).getResult().get("accessToken");
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] getAccessToken error,", e);
            return null;
        }
    }
    private int getfeatureId(String teamId) {
        String url = kspayOpenApiUrl.get() + "/api/kdev/openapi/team/kdevResource?taskId=" + teamId + "&type=feature";
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + getAccessToken());
        try {
            Response response = httpUtils.get(url, headers);
            if (!response.isSuccessful()) {
                throw new BizException(FEATUREID_GET_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(FEATUREID_GET_NULL);
            }
            log.info("[KspayTeamResourceServiceImpl] getfeatureId res: {}", response.body().string());
            KdevResourceResponse kdevResourceResponse = ObjectMapperUtils.fromJSON(response.body().string(), KdevResourceResponse.class);
            return kdevResourceResponse.getData().getList(0).getId();
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] getfeatureId error,", e);
            return -1;
        }
    }
    public List<Integer> getfeatureListbybiz(String bizSpaceName, long startTime, long endTime,
                                             String pageNo, String pageSize) {
        String url = kspayOpenApiUrl.get() + "/api/kdev/openapi/business/space/feature/list?" + "bizSpaceName=" + bizSpaceName
                + "&startTime=" + startTime + "&endTime=" + endTime + "&pageNo=" + pageNo + "&pageSize=" + pageSize;
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + getAccessToken());
        try {
            Response response = httpUtils.get(url, headers);
            if (!response.isSuccessful()) {
                throw new BizException(FEATUREID_GET_BY_BIZ_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(FEATUREID_GET_BY_BIZ_NULL);
            }
            String res = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] getfeatureListbybiz res: {}", res);
            KdevFeatureListResponse kdevFeatureListResponse = ObjectMapperUtils.fromJSON(res, KdevFeatureListResponse.class);
            List<Integer> featureIdList = new ArrayList<>();
            for (KdevFeatureList kdevFeatureList : kdevFeatureListResponse.getData().getListList()) {
                featureIdList.add(kdevFeatureList.getId());
            }
            return featureIdList;
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] getfeatureListbybiz error,", e);
            return new ArrayList<>();
        }
    }
    public KdevFeatureDetailData getfeaturedetail(int featureId) {
        String url = kspayOpenApiUrl.get() + "/api/kdev/openapi/feature/detail?" + "featureId=" + featureId;
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + getAccessToken());
        try {
            Response response = httpUtils.get(url, headers);
            if (!response.isSuccessful()) {
                throw new BizException(FEATURE_DETAIL_GET_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(FEATURE_DETAIL_GET_NULL);
            }
            String res = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] getfeaturedetail res: {}", res);
            KdevFeatureDetailResponse kdevFeatureDetailResponse = ObjectMapperUtils.fromJSON(res, KdevFeatureDetailResponse.class);
            return kdevFeatureDetailResponse.getData();
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] getfeaturedetail error,", e);
            return null;
        }
    }
    public BranchInfoData getBaseInfoWithRelationInfoByBranchId(int branchId) {
        String url = kspayOpenApiUrl.get() + "/api/kdev/openapi/branch/getBaseInfoWithRelationInfo?" + "id=" + branchId;
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + getAccessToken());
        try {
            Response response = httpUtils.get(url, headers);
            if (!response.isSuccessful()) {
                throw new BizException(GETBASEINFO_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(GETBASEINFO_NULL);
            }
            String res = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] getBaseInfoWithRelationInfoByBranchId res: {}", res);
            BranchInfoResponse branchInfoResponse = ObjectMapperUtils.fromJSON(res, BranchInfoResponse.class);
            return branchInfoResponse.getData();
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] getBaseInfoWithRelationInfoByBranchId error,", e);
            return null;
        }
    }
    @Override
    public ArrayList<String> fromBranchIdGetQa(Integer branchId) {
        String url = kspayOpenApiUrl.get() + "/api/kdev/openapi/branch/getBaseInfoWithRelationInfo?" + "id=" + branchId;
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + getAccessToken());
        try {
            Response response = httpUtils.get(url, headers);
            if (!response.isSuccessful()) {
                throw new BizException(GETBASEINFO_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(GETBASEINFO_NULL);
            }
            String res = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] getBaseInfoWithRelationInfoByBranchId res: {}", res);
            BranchInfoResponse branchInfoResponse = ObjectMapperUtils.fromJSON(res, BranchInfoResponse.class);
            ArrayList<String> qalist = new ArrayList<>();
            for (int i = 0; i < branchInfoResponse.getData().getFeatureListCount(); i++) {
                for (int k = 0; k < branchInfoResponse.getData().getFeatureList(i).getDevManagersCount(); k++) {
                    qalist.add(branchInfoResponse.getData().getFeatureList(i).getDevManagers(k).getUsername());
                }
            }
            for (int i = 0; i < branchInfoResponse.getData().getFeatureListCount(); i++) {
                for (int j = 0; j < branchInfoResponse.getData().getFeatureList(i).getQaManagersCount(); j++) {
                    qalist.add(branchInfoResponse.getData().getFeatureList(i).getQaManagers(j).getUsername());
                }
            }
            if (qalist != null && qalist.size() != 0) {
                return qalist;
            } else {
                return Lists.newArrayList();
            }
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] getBaseInfoWithRelationInfoByBranchId error,", e);
            return null;
        }
    }
    public void sendMessage(JSONArray userNames, String repoName, String branchName, Integer featureViewId, String riskStatus) {
        String url = kspayOpenApiUrl.get() + "/openapi/v2/message/batch/send?appKey="
                + "5d564bdd-4a67-4cd1-93b0-06e785e7a054&secretKey=6d507869e0c14e9ba9b7db0d2ac98aa3";
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Authorization", "Bearer " + getAccessMessageToken());
        /**
         * {
         *   "usernames":["通知人"],
         *   "msgType":"text",
         *   "text":{
         *     "content":"通知内容"
         *   }
         * }
         */
        JSONObject message = new JSONObject();
        try {
            message.put("usernames", userNames);
            message.put("msgType", "markdown");
            JSONObject markdown = new JSONObject();
            String color = riskStatus.equals("未通过") ? "red" : "green";
            String testResult = riskStatus.equals("未通过")
                    ? "存在资金安全风险"
                    : riskStatus;
            markdown.put("content",
                    "应用: " + repoName + "<br>"
                            + "分支: " + branchName + "<br>"
                            + "结果: <font color=" + color + ">" + testResult + "</font> <br>"
                            + "<font color=black>以下🔗需人工查看并确认:</font> <br>"
                            + "[风险详情点击查看]"
                            + "("
                            + "https://kwaishop-risk.staging.kuaishou.com/kspay/kspayRiskFeatureQuery?featureId=" + featureViewId + ")"
            );
            message.put("markdown", markdown);
//            message.put("usernames", userNames);
//            message.put("msgType", "text");
//            JSONObject text = new JSONObject();
//            text.put("content",
//                    "应用: " + repoName + "\n"
//                            + "分支:kspayQueryRiskByRepoAndBranch " + branchName + "\n"
//                            + "资金风险结果: " + riskStatus + "\n"
//                            + "风险详情: "
//                            + "https://kwaishop-risk.staging.kuaishou.com/kspay/kspayQueryRiskByRepoAndBranch?repoName="
//                            + repoName + "&branchName=" + branchName);
//            message.put("text", text);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            Response response = httpUtils.post(url, headers, message.toString());
            if (!response.isSuccessful()) {
                throw new BizException(GETBASEINFO_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(GETBASEINFO_NULL);
            }
            String res = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] sendMessage res: {}", res);
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] sendMessage error,", e);
        }
    }
    private String getAccessMessageToken() {
        Map<String, OpenApiAppConfig> openApiAppConfig = QaRiskJsonMapConfigKey.openApiAppKey.getMap();
        String url = kspayOpenApiUrl.get() + getAccessTokenPath + "appKey=5d564bdd-4a67-4cd1-93b0-06e785e7a054"
                + "&secretKey=6d507869e0c14e9ba9b7db0d2ac98aa3";
        try {
            Response response = httpUtils.get(url, null);
            if (!response.isSuccessful()) {
                throw new BizException(ACCESSTOKEN_GET_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(ACCESSTOKEN_GET_NULL);
            }
            String res = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] getAccessToken res: {}", res);
            return ObjectMapperUtils.fromJSON(res, OpenApiAccessTokenBO.class).getResult().get("accessToken");
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] getAccessToken error,", e);
            return null;
        }
    }
    public static void main(String[] args) {
        ArrayList<String> qalist = new ArrayList<>();
        //qalist = new KspayTeamResourceServiceImpl().fromBranchIdGetQa(3817062);
//        JSONArray usernames = new JSONArray(Arrays.toString(qalist.toArray()));
        //new KspayTeamResourceServiceImpl().sendMessage(new JSONArray(qalist), "kuaishou-pay", "dou_trackiing", "未通过");
        System.out.println("KspayTeamResourceServiceImpl.main:" + qalist.toString());
        //qalist.add("renpan");
        //qalist.add("renpan");
        //qalist = new KspayTeamResourceServiceImpl().fromBranchIdGetQa(3884724);
        // JSONArray usernames = new JSONArray(Arrays.toString(qalist.toArray()));
        new KspayTeamResourceServiceImpl().sendMessage(new JSONArray(qalist), "kuaishou-pay", "dou_tracking", Integer.getInteger("54976"), "未通过");
//        System.out.println("KspayTeamResourceServiceImpl.main:" + qalist.toString());
//        System.out.println("KspayTeamResourceServiceImpl.main1:" + String.join(",", qalist));
    }
    //    public static void main(String[] args) {
//        Long timeBefore = System.currentTimeMillis();
//        AccuracyData methodCovInfos = new KspayTeamResourceServiceImpl().getAccuracyData("kspay-core", "feature_20240322_pay_msg");
//        Long timeAfter = System.currentTimeMillis();
//        System.out.println(methodCovInfos);
//        System.out.println(timeAfter - timeBefore);
//    }
    // 如果isReturnEntry是false【不传默认false，不返回】，则不会返回allEntry字段，降低查询耗时
    public AccuracyData getAccuracyData(String application, String branch, boolean isReturnEntry, boolean needLongPost) {
        Map<String, String> value = new HashMap<>();
        value.put("application", application);
        value.put("branch", branch);
        String isReturnEntryStr = isReturnEntry ? "true" : "false";
        value.put("isReturnEntry", isReturnEntryStr);
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        // headers.put("trace-context", "{\"lanid\":\"PRT.test\"}");
        try {
            log.info("[KspayTeamResourceServiceImpl] getAccuracyData : req{}", value);
            // needLongPost 决定是否需要使用长连接
            Response response = needLongPost
                    ? httpUtilsLongTime.post(kspayAccuracyUrl.get(), headers, value)
                    : httpUtils.post(kspayAccuracyUrl.get(), headers, value);
            if (!response.isSuccessful()) {
                throw new BizException(ACCURACY_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(ACCURACY_NULL);
            }
            String res = response.body().string();
            log.info("[KspayTeamResourceServiceImpl] getAccuracyData res: {}", res);
            AccuracyData accuracyData = ObjectMapperUtils.fromJSON(res, AccuracyData.class);
            if (accuracyData != null && accuracyData.hasData()) {
                // 判断有资损精准是否会返回表字段，没有需要报错
                if (accuracyData.getData().getFinnaceLossCnt() > 0) {
                    List<FinanceMethod> financeMethods = accuracyData.getData().getFinanceMethodsList();
                    if (financeMethods == null || financeMethods.isEmpty()
                            || financeMethods.stream()
                            .allMatch(fm -> fm.getMethodInfo() == null
                                    || fm.getMethodInfo().getPayFinanceLossPointsList() == null
                                    || fm.getMethodInfo().getPayFinanceLossPointsList().isEmpty())) {
                        log.error("getAccuracyData 缺少表字段信息:{}, 仓库 && 分支:{},{}",
                                ACCURACY_PAY_FINANCE_NULL, application, branch);
                    }
                }
                return accuracyData;
            } else {
                log.warn("[KspayTeamResourceServiceImpl] getAccuracyData is null.");
                return null;
            }
        } catch (SocketTimeoutException socketTimeoutException) {
            log.warn("[KspayTeamResourceServiceImpl] getAccuracyData timeout,", socketTimeoutException);
            return null;
        } catch (SocketException socketException) {
            log.warn("[KspayTeamResourceServiceImpl] getAccuracyData socket Exception,", socketException);
            return null;
        } catch (Exception e) {
            log.error("[KspayTeamResourceServiceImpl] getAccuracyData error,", e);
            return null;
        }
    }
    public AccuracyData getAccuracyDataForRelateRiskQuery(String application, String branch, boolean isReturnEntry) {
        Map<String, String> value = new HashMap<>();
        value.put("application", application);
        value.put("branch", branch);
        String isReturnEntryStr = isReturnEntry ? "true" : "false";
        value.put("isReturnEntry", isReturnEntryStr);
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        try {
            log.info("[getAccuracyDataForRelateRiskQuery] getAccuracyData : req{}", value);
            Response response = httpUtilsShortTime.post(kspayAccuracyUrl.get(), headers, value);
            if (!response.isSuccessful()) {
                throw new BizException(ACCURACY_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(ACCURACY_NULL);
            }
            String res = response.body().string();
            log.info("[getAccuracyDataForRelateRiskQuery] getAccuracyData res: {}", res);
            AccuracyData accuracyData = ObjectMapperUtils.fromJSON(res, AccuracyData.class);
            if (accuracyData != null && accuracyData.hasData()) {
                log.info("[getAccuracyDataForRelateRiskQuery] afterParseAccuracyData res: {}",
                        ProtoPrinterUtils.protoToJson(accuracyData));
                return accuracyData;
            } else {
                log.warn("[getAccuracyDataForRelateRiskQuery] getAccuracyData is null.");
                return null;
            }
        } catch (SocketTimeoutException socketTimeoutException) {
            log.error("[getAccuracyDataForRelateRiskQuery] getAccuracyData timeout,", socketTimeoutException);
            return null;
        } catch (SocketException socketException) {
            log.error("[getAccuracyDataForRelateRiskQuery] getAccuracyData socket Exception,", socketException);
            return null;
        } catch (Exception e) {
            log.error("[getAccuracyDataForRelateRiskQuery] getAccuracyData error,", e);
            return null;
        }
    }
    // todo: 统计使用，后续下掉
    public AccuracyData getAccuracyDataForRelateRiskQueryReturnException(String application, String branch,
                                                                         boolean isReturnEntry) throws Exception {
        Map<String, String> value = new HashMap<>();
        value.put("application", application);
        value.put("branch", branch);
        String isReturnEntryStr = isReturnEntry ? "true" : "false";
        value.put("isReturnEntry", isReturnEntryStr);
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        log.info("[getAccuracyDataForRelateRiskQuery] getAccuracyData : req{}", value);
        try {
            Response response = httpUtilsLongTime.post(kspayAccuracyUrl.get(), headers, value);
            if (!response.isSuccessful()) {
                throw new BizException(ACCURACY_ERROR);
            }
            if (response.body() == null) {
                throw new BizException(ACCURACY_NULL);
            }
            String res = response.body().string();
            log.info("[getAccuracyDataForRelateRiskQuery] getAccuracyData res: {}", res);
            AccuracyData accuracyData = ObjectMapperUtils.fromJSON(res, AccuracyData.class);
            if (accuracyData != null && accuracyData.hasData()) {
                log.info("[getAccuracyDataForRelateRiskQuery] afterParseAccuracyData res: {}",
                        ProtoPrinterUtils.protoToJson(accuracyData));
                return accuracyData;
            } else {
                log.warn("[getAccuracyDataForRelateRiskQuery] getAccuracyData is null.");
                return null;
            }
        } catch (SocketTimeoutException socketTimeoutException) {
            throw socketTimeoutException;
        } catch (SocketException socketException) {
            log.error("[getAccuracyDataForRelateRiskQuery] getAccuracyData socket Exception,", socketException);
            throw socketException;
        } catch (Exception e) {
            log.error("[getAccuracyDataForRelateRiskQuery] getAccuracyData error,", e);
            throw e;
        }
    }
    // 请求精准增加重试机制
    public AccuracyData getAccuracyDataWithRepeat(String application, String branch, boolean isReturnEntry, boolean needLongPost) {
        int maxRetries = 3; // 设置最大重试次数
        long retryDelay = 1000; // 设置每次重试之间的延迟（毫秒）
        Map<String, String> value = new HashMap<>();
        value.put("application", application);
        value.put("branch", branch);
        String isReturnEntryStr = isReturnEntry ? "true" : "false";
        value.put("isReturnEntry", isReturnEntryStr);
        Map<String, String> headers = Maps.newHashMap();
        headers.put("Content-Type", "application/json");
        for (int i = 0; i < maxRetries; i++) {
            try {
                log.info("[KspayTeamResourceServiceImpl] getAccuracyData attempt {} : req{}", i + 1, value);
                // 是否使用长连接
                Response response = needLongPost
                        ? httpUtilsLongTime.post(kspayAccuracyUrl.get(), headers, value)
                        : httpUtils.post(kspayAccuracyUrl.get(), headers, value);
                if (!response.isSuccessful()) {
                    throw new BizException(ACCURACY_ERROR);
                }
                if (response.body() == null) {
                    throw new BizException(ACCURACY_NULL);
                }
                String res = response.body().string();
                log.info("[KspayTeamResourceServiceImpl] getAccuracyData res: {}", res);
                AccuracyData accuracyData = ObjectMapperUtils.fromJSON(res, AccuracyData.class);
                if (accuracyData != null && accuracyData.hasData()) {
                    return accuracyData;
                } else {
                    log.warn("[KspayTeamResourceServiceImpl] getAccuracyData: Data not available or empty.");
                    return null;
                }
            } catch (SocketTimeoutException | SocketException e) {
                if (i < maxRetries - 1) {
                    log.warn("[KspayTeamResourceServiceImpl] getAccuracyData attempt {} failed due to {}, retrying in {} ms...",
                            i + 1, e.getMessage(), retryDelay);
                    try {
                        Thread.sleep(retryDelay); // 添加延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted while waiting to retry", ie);
                    }
                } else {
                    log.error("[KspayTeamResourceServiceImpl] getAccuracyData max retries reached, giving up", e);
                }
            } catch (Exception e) {
                log.error("[KspayTeamResourceServiceImpl] getAccuracyData error,", e);
                return null;
            }
        }
        return null; // 如果所有尝试都失败，返回null
    }
//    public static void main(String[] args) {
//        KspayTeamResourceServiceImpl kspayTeamResourceServiceImpl = new KspayTeamResourceServiceImpl();
//
//        List<Integer> featureIdList  = kspayTeamResourceServiceImpl.getfeatureListbybiz("支付中台", 1686585600000L, 1686656429660L, "", "");
//        List<KdevFeatureDetailData> kdevFeatureDetailDataList = new ArrayList<>();
//        for (int i:featureIdList) {
//            KdevFeatureDetailData kdevFeatureDetailData = kspayTeamResourceServiceImpl.getfeaturedetail(i);
//            kdevFeatureDetailDataList.add(kdevFeatureDetailData);
//        }
//        int branchId = kdevFeatureDetailDataList.get(0).getBranchList(1).getBranchId();
//        BranchInfoData branchInfoData = kspayTeamResourceServiceImpl.getBaseInfoWithRelationInfoByBranchId(3191327);
//
//
//        int a = kspayTeamResourceServiceImpl.getfeatureId("********");
//        FeatureInfoRequest featureInfoRequest = FeatureInfoRequest.newBuilder()
//                .setTeamId("********")
//                .build();
//        List<FeatureInfo> b = kspayTeamResourceServiceImpl.queryFeatureInfo(featureInfoRequest);
//
//        QueryTeamTaskRequest queryTeamTaskRequest = QueryTeamTaskRequest.newBuilder()
//                .setKeyword("绑卡1.0兼容银联实名不一致错误")
//                .setOperator("zengzhen01")
//                .setLimit(10)
//                .setTaskGroup(0)
//                .setOffset(0)
//                .build();
//        List<TaskInfo> c = kspayTeamResourceServiceImpl.queryTeamTask(queryTeamTaskRequest);
//
//        System.out.println("");
//    }
}