package com.kuaishou.kwaishop.qa.risk.center.db.mapper.config;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-15
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface ConfigMapper {

    @Select("select val from config where name = #{name}")
    String getConfigByName(@Param("name") String name);
}
