package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
public enum UserAccountTypeEnum {

    PLATFORM(1, "平台导入类型账号"),
    PERSONAL(2, "个人导入平台账号"),
    ;

    private final Integer code;
    private final String desc;

    UserAccountTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static UserAccountTypeEnum of(Integer code) {
        for (UserAccountTypeEnum typeEnum: values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (UserAccountTypeEnum typeEnum: values()) {
            EnumInfo enumInfo = EnumInfo.newBuilder()
                    .setValue(typeEnum.getCode())
                    .setDesc(typeEnum.getDesc())
                    .build();
            res.add(enumInfo);
        }
        return res;
    }
}
