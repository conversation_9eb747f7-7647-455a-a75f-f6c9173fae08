package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressSerBO;

public interface StressSerService {
    PageBO<StressServiceDO> queryPageList(QueryStressSerBO queryBO);

    PageBO<StressServiceDO> queryPageListWithScenario(QueryStressSerBO  queryBO, List<Long> scenarioIds);

    PageBO<StressServiceDO> queryPageListWithService(QueryStressSerBO  queryBO, List<Long> serviceIds);

    StressServiceDO queryInterfaceDetail(Long id);

    Integer createOrUpdateInterface(StressServiceDO serBO);

    Integer batchCreateInterface(List<StressServiceDO> serBOs);
}
