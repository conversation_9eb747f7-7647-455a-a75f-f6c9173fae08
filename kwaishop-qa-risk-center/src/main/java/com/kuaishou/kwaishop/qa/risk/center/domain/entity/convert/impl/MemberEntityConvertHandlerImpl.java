package com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.impl;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertAbstract;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.CreateEntityRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.UpdateEntityRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-24
 */
@Component
public class MemberEntityConvertHandlerImpl extends EntityConvertAbstract implements EntityConvertHandler {
    @Override
    public EntityDO buildCreateDO(EntityBO entityBO) {
        return EntityDO.builder()
                .entityType(getEntityType())
                .entityId(entityBO.getEntityId())
                .name(entityBO.getName())
                .status(entityBO.getStatus())
                .creator(entityBO.getOperator())
                .modifier(entityBO.getOperator())
                .build();
    }

    @Override
    public EntityDO buildModifyDO(EntityDO existDO, EntityBO entityBO) {
        existDO.setStatus(entityBO.getStatus());
        existDO.setModifier(entityBO.getOperator());
        return existDO;
    }

    @Override
    public Integer getEntityType() {
        return EntityTypeEnum.MEMBER_TYPE.getCode();
    }

    @Override
    public EntityBO buildCreateBO(CreateEntityRequest request) {
        return EntityBO.builder()
                .entityType(request.getEntityType())
                .entityId(request.getEntityId())
                .name(request.getName())
                .operator(request.getOperator())
                .status(request.getStatus())
                .build();
    }

    @Override
    public EntityBO buildModifyBO(UpdateEntityRequest request) {
        return EntityBO.builder()
                .id(request.getId())
                .entityType(request.getEntityType())
                .entityId(request.getEntityId())
                .name(request.getName())
                .operator(request.getOperator())
                .status(request.getStatus())
                .build();
    }

    @Override
    public EntityQueryBO buildQueryListBO(QueryEntityListRequest request) {
        return EntityQueryBO.builder()
                .id(request.getId())
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .nameEq(request.getName())
                .status(request.getStatus())
                .build();
    }

    @Override
    public EntityQueryBO buildQueryPageListBO(QueryEntityPageListRequest request) {
        return EntityQueryBO.builder()
                .id(request.getId())
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .nameEq(request.getName())
                .status(request.getStatus())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
    }
}
