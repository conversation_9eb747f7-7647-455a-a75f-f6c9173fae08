package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.AUTU_USER_NOT_EXIST;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.UserAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAuthBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.DatafactoryService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAuthService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateAllUserInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UpdateUserAuthRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAccountDTO;
import com.kuaishou.old.util.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-24
 */
@Service
@Slf4j
public class UserAuthBizService implements com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.UserAuthBizService {

    @Autowired
    private UserAuthService userAuthService;

    @Autowired
    private DatafactoryService datafactoryService;

    @Autowired
    private UserAccountBizService userAccountBizService;

    @Override
    public void updateAuthInfo(UpdateUserAuthRequest updateUserAuthRequest) {
        userAuthService.updateAuthInfo(updateUserAuthRequest.getUserId());
    }

    @Override
    public void updateAutoAllAuth() {
        log.info("开始自动更新");

        // 获取需要更新的内容
        List<UserAccountDTO> userAccountDTOS = userAccountBizService.queryUserAccountList(QueryUserAccountRequest.newBuilder().build());
        List<Long> userIdsAll = userAccountDTOS.stream()
                .map(UserAccountDTO::getUserId)
                .collect(Collectors.toList());

        userIdsAll.forEach(id -> {
            log.info("自动更新权限成功，userId = {}", id);
            userAuthService.updateAuthInfo(id);
        });
    }

    @Override
    public void insertAuthInfo(Long userId) {
        userAuthService.insertAuthInfo(userId);
    }

    @Override
    public List<UserAuthDO> queryUserAuthRecords(UserAuthBO userAuthBO) {
        return userAuthService.queryUserAuthRecords(userAuthBO);
    }

    @Override
    public void updateUserAllAuthInfo(UpdateAllUserInfoRequest request) {
        //1.判断哪些和实际不一样，需要调数据工厂的接口进行更新
        Map<String, Integer> authInfoMap = checkNeedUpdate(request);
        String userId = String.valueOf(request.getUserId());
        if (authInfoMap.isEmpty()) {
            if (!StringUtil.isNullOrEmpty(request.getComment())) {
                List<UserAuthDO> userAuthDOS = userAuthService.queryUserAuthRecords(UserAuthBO.builder()
                        .userId(request.getUserId())
                        .build());
                userAuthService.update(UserAuthDO.builder()
                                .id(userAuthDOS.get(0).getId())
                                .comment(request.getComment())
                                .build());
            }
            return;
        }
        authInfoMap.forEach((k, v) -> {
            datafactoryService.openPermission(userId, k, v);
        });
        //2.更新下comment和最新的权限
        UserAuthDO userAuthDO = userAuthService.queryAllUserAuth(request.getUserId());
        userAuthDO.setComment(request.getComment());
        List<UserAuthDO> userAuthDOS = userAuthService.queryUserAuthRecords(UserAuthBO.builder()
                .userId(request.getUserId())
                .build());
        userAuthDO.setId(userAuthDOS.get(0).getId());
        userAuthService.update(userAuthDO);
    }

    public Map<String, Integer> checkNeedUpdate(UpdateAllUserInfoRequest request) {
        Map<String, Integer> res = new HashMap<>();
        List<UserAuthDO> userAuthDOS = userAuthService.queryUserAuthRecords(UserAuthBO.builder().userId(request.getUserId()).build());
        if (userAuthDOS.isEmpty()) {
            throw new BizException(AUTU_USER_NOT_EXIST);
        }

        UserAuthDO userAuthDO = userAuthDOS.get(0);
        if (request.getDistributorPermission() != userAuthDO.getDistributorPermission()) {
            res.put("SELLER", request.getDistributorPermission());
        }
        if (request.getPromoterPermission() != userAuthDO.getCelebrityPermission()) {
            res.put("PROMOTER", request.getPromoterPermission());
        }
        // TODO: 团长和快赚客后面做。
        return res;
    }
}
