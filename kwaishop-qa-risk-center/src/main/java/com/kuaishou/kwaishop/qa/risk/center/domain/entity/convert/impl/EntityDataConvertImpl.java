package com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.ApplicationConstants.SYSTEM;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDataDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityDataConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDataBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityDataDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-30
 */
@Component
public class EntityDataConvertImpl implements EntityDataConvert {


    @Override
    public EntityDataDO buildCreateDO(EntityDataBO entityDataBO) {
        return EntityDataDO.builder()
                .entityId(entityDataBO.getEntityId())
                .entityType(entityDataBO.getEntityType())
                .dataType(entityDataBO.getDataType())
                .dataInfo(entityDataBO.getDataInfo())
                .dt(entityDataBO.getDt())
                .createTime(System.currentTimeMillis())
                .updateTime(System.currentTimeMillis())
                .creator(SYSTEM)
                .modifier(SYSTEM)
                .build();
    }

    @Override
    public EntityDataBO buildCreateBO(Long entityId, Integer entityType, Integer dateType, String dt, String dataInfo) {
        return EntityDataBO.builder()
                .entityId(entityId)
                .entityType(entityType)
                .dataType(dateType)
                .dt(dt)
                .dataInfo(dataInfo)
                .build();
    }

    @Override
    public List<EntityDataDTO> buildEntityDataDTO(List<EntityDataDO> entityDataDOS, Map<Long, EntityDO> entityDOMap) {
        return entityDataDOS.stream().map(e -> buildEntityDataDTO(e, entityDOMap)).collect(Collectors.toList());
    }

    @Override
    public EntityDataDTO buildEntityDataDTO(EntityDataDO entityDataDO, Map<Long, EntityDO> entityDOMap) {
        return EntityDataDTO.newBuilder()
                .setDataInfo(entityDataDO.getDataInfo())
                .setDt(entityDataDO.getDt())
                .setEntityType(entityDataDO.getEntityType())
                .setEntityId(entityDataDO.getEntityId())
                .setId(entityDataDO.getId())
                .setName(entityDOMap.get(entityDataDO.getEntityId()).getName())
                .build();
    }
}
