package com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertAbstract;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityBaseBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.CreateEntityRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.UpdateEntityRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@Component
public class EntityBaseConvertHandlerImpl extends EntityConvertAbstract implements EntityConvertHandler {

    @Override
    public EntityDO buildCreateDO(EntityBO entityBO) {
        EntityBaseBO entityBaseBO = (EntityBaseBO) entityBO;
        EntityDO entityDO = EntityDO.builder()
                .entityType(entityBaseBO.getEntityType())
                .entityId(entityBaseBO.getEntityId())
                .name(entityBaseBO.getName())
                .status(entityBaseBO.getStatus())
                .creator(entityBaseBO.getOperator())
                .modifier(entityBaseBO.getOperator())
                .extra1(entityBaseBO.getExtra1())
                .extra2(entityBaseBO.getExtra2())
                .extra3(entityBaseBO.getExtra3())
                .build();
        if (StringUtils.isNotBlank(entityBaseBO.getExt())) {
            entityDO.setExt(entityBaseBO.getExt());
        }
        return entityDO;
    }

    @Override
    public EntityDO buildModifyDO(EntityDO existDO, EntityBO entityBO) {
        EntityBaseBO entityBaseBO = (EntityBaseBO) entityBO;
        existDO.setEntityId(entityBaseBO.getEntityId());
        existDO.setName(entityBaseBO.getName());
        existDO.setStatus(entityBaseBO.getStatus());
        existDO.setModifier(entityBaseBO.getOperator());
        existDO.setExtra1(entityBaseBO.getExtra1());
        existDO.setExtra2(entityBaseBO.getExtra2());
        existDO.setExtra3(entityBaseBO.getExtra3());
        if (StringUtils.isNotBlank(entityBaseBO.getExt())) {
            existDO.setExt(entityBaseBO.getExt());
        }
        return existDO;
    }


    @Override
    public Integer getEntityType() {
        return EntityTypeEnum.DEFAULT.getCode();
    }

    @Override
    public EntityBO buildCreateBO(CreateEntityRequest request) {
        return EntityBaseBO.builder()
                .entityType(request.getEntityType())
                .entityId(request.getEntityId())
                .name(request.getName())
                .status(request.getStatus())
                .ext(request.getExt())
                .operator(request.getOperator())
                .extra1(request.getExtra1())
                .extra2(request.getExtra2())
                .extra3(request.getExtra3())
                .build();
    }

    @Override
    public EntityBO buildModifyBO(UpdateEntityRequest request) {
        return EntityBaseBO.builder()
                .id(request.getId())
                .entityType(request.getEntityType())
                .entityId(request.getEntityId())
                .name(request.getName())
                .status(request.getStatus())
                .ext(request.getExt())
                .operator(request.getOperator())
                .extra1(request.getExtra1())
                .extra2(request.getExtra2())
                .extra3(request.getExtra3())
                .operator(request.getOperator())
                .build();
    }

    @Override
    public EntityQueryBO buildQueryListBO(QueryEntityListRequest request) {
        return EntityQueryBO.builder()
                .id(request.getId())
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .nameEq(request.getName())
                .status(request.getStatus())
                .build();
    }

    @Override
    public EntityQueryBO buildQueryPageListBO(QueryEntityPageListRequest request) {
        return EntityQueryBO.builder()
                .id(request.getId())
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .nameEq(request.getName())
                .status(request.getStatus())
                .pageSize(request.getPageSize())
                .pageNo(request.getPageNo())
                .build();
    }
}
