package com.kuaishou.kwaishop.qa.risk.center.db.mapper.problem;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.problem.ProblemDetailDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-02
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface ProblemDetailMapper extends BaseMapper<ProblemDetailDO> {

    @Update("update problem_detail set deleted = 1, modifier = #{operator} where id = #{id}")
    int logicDeleted(@Param("operator") String operator, @Param("id") Long id);
}
