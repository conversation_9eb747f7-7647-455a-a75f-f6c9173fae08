package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressServiceBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressServiceRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressScenarioRecordBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressSerRecordService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class StressSerRecordServiceImpl implements StressSerRecordService {

    @Resource
    private StressServiceRecordDAO serRecordDAO;

    @Resource
    private StressServiceBaseDAO baseDAO;
    @Override
    public int createStressServiceRecord(StressScenarioRecordBO recordBO) {
        return 0;
    }
    @Override
    public StressScenarioRecordDO getStressScenarioRecord() {
        return null;
    }

    @Override
    public List<StressServiceRecordDO> getStressSerRecordWithBase(Long serviceRecordId) {
        List<StressServiceRecordDO> list = new ArrayList<>();
        StressServiceRecordDO serviceRecordDO = serRecordDAO.queryStressSerRecordById(serviceRecordId);
        if (serviceRecordDO != null) {
            list.add(serviceRecordDO);
            Long serviceId = serviceRecordDO.getServiceId();
            StressServiceBaseDO baseDO = baseDAO.queryStressServiceBaseByServiceId(serviceId);
            StressServiceRecordDO baseRecordDO = StressServiceRecordDO.builder()
                    .serviceId(serviceRecordDO.getServiceId())
                    .serviceName(serviceRecordDO.getServiceName())
                    .scenarioId(serviceRecordDO.getScenarioId())
                    .scenarioName(serviceRecordDO.getScenarioName())
                    .scenarioRecordId(serviceRecordDO.getScenarioRecordId())
                    .scenarioRecordName(serviceRecordDO.getScenarioRecordName())
                    .avgCpu(baseDO.getAvgCpu())
                    .avgDisk(baseDO.getAvgDisk())
                    .avgExceptions(baseDO.getAvgExceptions())
                    .avgRam(baseDO.getAvgRam())
                    .avgRisk(baseDO.getAvgRisk())
                    .avgThreadPools(baseDO.getAvgThreadPools())
                    .maxCpu(baseDO.getMaxCpu())
                    .maxDisk(baseDO.getMaxDisk())
                    .maxRam(baseDO.getMaxRam())
                    .maxExceptions(baseDO.getMaxExceptions())
                    .maxThreadPools(baseDO.getMaxThreadPools())
                    .createTime(baseDO.getCreateTime())
                    .updateTime(baseDO.getUpdateTime())
                    .status(1)
                    .id(0L)
                    .build();
            list.add(baseRecordDO);

        }
        return list;
    }
}
