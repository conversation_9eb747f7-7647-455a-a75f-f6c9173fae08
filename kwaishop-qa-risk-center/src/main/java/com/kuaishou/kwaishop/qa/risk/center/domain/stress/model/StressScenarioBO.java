package com.kuaishou.kwaishop.qa.risk.center.domain.stress.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StressScenarioBO {
    private Long id;
    /**
     * 场景所有人id
     */
    private Long ownerId;

    /**
     * 场景名
     */
    private String name;

    /**
     * 场景状态: 0-创建完成, 1-删除
     */
    private Integer status;

    /**
     * 外部url
     */
    private String outUrl;

    /**
     * 外部系统id
     */
    private String outId;

    private Integer deleted;
}
