package com.kuaishou.kwaishop.qa.risk.center.db.mapper.entity;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDataDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-29
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface EntityDataMapper extends BaseMapper<EntityDataDO> {


    @Insert("<script>"
            + "INSERT INTO `entity_data`(`id`, `entity_type`, `entity_id`, `data_type`, `data_info`, `dt`, "
            + "<if test='entityData.ext != null'> "
            + "`ext`,"
            + "</if>"
            + " `create_time`, `update_time`, `creator`, `modifier`)"
            + "VALUES"
            + "(#{entityData.id}, #{entityData.entityType}, #{entityData.entityId}, #{entityData.dataType},"
            + " #{entityData.dataInfo}, #{entityData.dt},"
            + "<if test='entityData.ext != null'> "
            + " #{entityData.ext},"
            + "</if>"
            + "#{entityData.createTime}, #{entityData.updateTime}, #{entityData.creator}, #{entityData.modifier})"
            + "ON DUPLICATE KEY UPDATE "
            + "data_info=#{entityData.dataInfo}, update_time=#{entityData.updateTime}, version = version+1,"
            + "<if test='entityData.ext != null'> "
            + "ext = #{entityData.ext},"
            + "</if>"
            + " modifier=#{entityData.modifier}"
            + "</script>")
    long insertOrUpdate(@Param("entityData") EntityDataDO entityDataDO);
}
