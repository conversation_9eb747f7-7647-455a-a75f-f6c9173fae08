package com.kuaishou.kwaishop.qa.risk.center.db.mapper.account;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;
import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.TAG_META;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagMetaDO;


/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 16:45
 * @注释
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface TagMetaMapper extends BaseMapper<TagMetaDO> {


    @Select("SELECT * FROM " + TAG_META + " WHERE tag_value = #{tagValue}")
    TagMetaDO selectByTagCode(@Param("tagValue") String tagValue);

    @Select("SELECT * FROM " + TAG_META + " WHERE tag_name = #{tagName}")
    TagMetaDO selectByTagName(@Param("tagName") String tagName);

    @Insert("INSERT INTO tag_meta "
            + "(id, tag_id, tag_name, tag_value, tag_subject_type, user_id, user_name, account_type, risk_level, "
            + "online_system_name,status,ext,create_time,update_time,creator,modifier,deleted,version) "
            + "VALUES (#{id}, #{tagId},#{tagName}, #{tagValue},1, #{userId}, "
            + "#{userName}, #{accountType},0, #{onlineSystemName},1, #{ext}, "
            + "#{createTime}, #{updateTime},#{creator}, #{modifier},0, 1)"
    )
    int insert(TagMetaDO tagMetaDO);

    @Select("SELECT * FROM tag_meta where deleted = 0")
    List<TagMetaDO> selectAll();

    @Update("update tag_meta set deleted = 1,  modifier = #{modifier}, "
            +
            " update_time = #{updateTime} where tag_value = #{tagValue}")
    void deleteTag(TagMetaDO tagMetaDO);


}
