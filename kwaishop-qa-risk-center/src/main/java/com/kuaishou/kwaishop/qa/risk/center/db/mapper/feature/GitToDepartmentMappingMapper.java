package com.kuaishou.kwaishop.qa.risk.center.db.mapper.feature;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.GitToDepartmentMappingParamDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface GitToDepartmentMappingMapper {
    @Select("select * from kspay_git_department_mapping where git_project_id = #{gitProjectId}")
    GitToDepartmentMappingParamDO queryMappingByProjectId(@Param("gitProjectId") Long gitProjectId);

    @Options(useGeneratedKeys = true, keyProperty = "id")
    @Insert("INSERT INTO kspay_git_department_mapping ("
            + "first_department_id, second_department_id, third_department_id,"
            + "first_department_name, second_department_name, third_department_name,"
            + "git_project_id, git_name, git_description,"
            + "creator, updater, create_time, update_time) "
            + "VALUES ("
            + "#{firstDepartmentId}, #{secondDepartmentId}, #{thirdDepartmentId},"
            + "#{firstDepartmentName}, #{secondDepartmentName}, #{thirdDepartmentName},"
            + "#{gitProjectId}, #{gitName}, #{gitDescription},"
            + "#{creator}, #{updater}, #{createTime}, #{updateTime}) "
            + "ON DUPLICATE KEY UPDATE "
            + "first_department_id = VALUES(first_department_id),"
            + "second_department_id = VALUES(second_department_id),"
            + "third_department_id = VALUES(third_department_id),"
            + "first_department_name = VALUES(first_department_name),"
            + "second_department_name = VALUES(second_department_name),"
            + "third_department_name = VALUES(third_department_name),"
            + "git_name = VALUES(git_name),"
            + "git_description = VALUES(git_description),"
            + "updater = VALUES(updater),"
            + "update_time = VALUES(update_time)")
    int insertOrUpdateMapping(GitToDepartmentMappingParamDO paramDO);

    @Insert("INSERT INTO kspay_git_department_mapping ("
            + "id, first_department_id, second_department_id, third_department_id,"
            + "first_department_name, second_department_name, third_department_name,"
            + "git_project_id, git_name, git_description,"
            + "creator, updater, create_time, update_time) "
            + "VALUES ("
            + "#{id}, #{firstDepartmentId}, #{secondDepartmentId}, #{thirdDepartmentId},"
            + "#{firstDepartmentName}, #{secondDepartmentName}, #{thirdDepartmentName},"
            + "#{gitProjectId}, #{gitName}, #{gitDescription},"
            + "#{creator}, #{updater}, #{createTime}, #{updateTime})")
    int insertMapping(GitToDepartmentMappingParamDO paramDO);

    @Update("UPDATE kspay_git_department_mapping SET "
            + "first_department_id = #{firstDepartmentId}, "
            + "second_department_id = #{secondDepartmentId}, "
            + "third_department_id = #{thirdDepartmentId}, "
            + "first_department_name = #{firstDepartmentName}, "
            + "second_department_name = #{secondDepartmentName}, "
            + "third_department_name = #{thirdDepartmentName}, "
            + "git_name = #{gitName}, "
            + "git_description = #{gitDescription}, "
            + "updater = #{updater}, "
            + "update_time = #{updateTime} "
            + "WHERE git_project_id = #{gitProjectId}")
    int updateMapping(GitToDepartmentMappingParamDO paramDO);
}
