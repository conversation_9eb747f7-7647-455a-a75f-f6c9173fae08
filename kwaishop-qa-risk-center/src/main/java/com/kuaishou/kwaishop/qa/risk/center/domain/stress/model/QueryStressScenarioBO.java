package com.kuaishou.kwaishop.qa.risk.center.domain.stress.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-09-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryStressScenarioBO {

    private Long id;
    /**
     * 实体类型
     */
    private Integer entityType;

    /**
     * 实体id
     */
    private Long entityId;

    /**
     * 组织名
     */
    private String name;

    private Integer deleted;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 页数
     */
    private Integer pageSize;
}

