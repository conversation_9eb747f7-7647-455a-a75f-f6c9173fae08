package com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataScenarioDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2023-03-15
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface DataScenarioMapper extends BaseMapper<DataScenarioDO> {

    @Update("update data_scenario set modifier = #{operator}, deleted = 1 where id = #{id}")
    int logicDeleted(@Param("operator") String operator, @Param("id") Long id);


    @Select("select * from data_scenario where  deleted=0 and name like CONCAT('%',#{name},'%') ")
    List<DataScenarioDO> queryDataScenarioLikeName(@Param("name") String name);

    @Select("select id from data_scenario  where deleted=0 and auto_trigger=1 ")
    List<Long> queryAutoScenarioIdList();

    @Select("select * from data_scenario  where deleted=0 and id = #{id} ")
    DataScenarioDO queryDataScenarioById(@Param("id") Long id);

    @Select("select * from data_scenario  where deleted=0 ")
    List<DataScenarioDO> queryDataScenario();

}
