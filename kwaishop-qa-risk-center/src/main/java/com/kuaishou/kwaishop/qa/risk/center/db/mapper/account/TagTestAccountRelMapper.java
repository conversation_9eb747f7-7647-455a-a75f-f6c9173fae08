package com.kuaishou.kwaishop.qa.risk.center.db.mapper.account;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;
import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.TAG_TEST_ACCOUNT_REL;

import java.util.List;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagTestAccountRelDO;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/15 16:46
 * @注释
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface TagTestAccountRelMapper extends BaseMapper<TagTestAccountRelDO> {

    @Select({"<script>", "SELECT * FROM tag_test_account_rel  WHERE deleted=0 ",
            "and tag_auth_status in ",
            "<foreach item='tagAuthStatus' collection='tagAuthStatusList' open='(' separator=',' close=')'>",
            "#{tagAuthStatus}",
            "</foreach>",
            "and id in ",
            "<foreach item='testAccountId' collection='ids' open='(' separator=',' close=')'>",
            "#{testAccountId}",
            "</foreach>",
            "</script>"})
    List<TagTestAccountRelDO> getTagListByIds(List<Long> ids, List<Integer> tagAuthStatusList);


    @Select({"<script>", "SELECT * FROM tag_test_account_rel  WHERE deleted=0 ",
            "and tag_auth_status in ",
            "<foreach item='tagAuthStatus' collection='tagAuthStatusList' open='(' separator=',' close=')'>",
            "#{tagAuthStatus}",
            "</foreach>",
            "and test_account_id in ",
            "<foreach item='testAccountId' collection='ids' open='(' separator=',' close=')'>",
            "#{testAccountId}",
            "</foreach>",
            "</script>"})
    List<TagTestAccountRelDO> getTagListBytestAccountIds(List<Long> ids, List<Integer> tagAuthStatusList);

    @Update("UPDATE tag_test_account_rel SET deleted = #{deleted},"
            + " update_time = #{updateTime}"
            + " WHERE test_account_id = #{testAccountId}"
            + " AND tag_value = #{tagValue}")
    void deleteFromAccount(@Param("testAccountId") Long id, @Param("tagValue") String tagCode,
                           @Param("deleted") Integer deleted, @Param("updateTime") Long updateTime);

    @Insert("INSERT INTO " + TAG_TEST_ACCOUNT_REL
            + " (test_account_id,b_id,kwai_id,tag_meta_id,tag_outer_id,tag_name,tag_value"
            + " ,tag_auth_status,apply_time,duration,due_time,create_time,update_time"
            + " ,creator,modifier,deleted,bpm)"
            + "VALUES (#{testAccountId}, 0, #{kwaiId}, #{tagMetaId}, #{tagOuterId},"
            + "#{tagName}, #{tagValue}, #{tagAuthStatus}, #{applyTime}, #{duration},"
            + "#{dueTime}, #{createTime}, #{updateTime}, "
            + "#{creator}, #{modifier}, 0 ,#{bpm})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(TagTestAccountRelDO tagTestAccountRelDO);

    @Select({"<script>", "SELECT * FROM tag_test_account_rel  WHERE deleted=0 ",
            "and tag_auth_status in ",
            "<foreach item='tagAuthStatus' collection='tagAuthStatusList' open='(' separator=',' close=')'>",
            "#{tagAuthStatus}",
            "</foreach>",
            "</script>"})
    List<TagTestAccountRelDO> getTagListByTestAccountIds(List<Integer> tagAuthStatusList);

    @Select({"<script>", "SELECT * FROM tag_test_account_rel  WHERE deleted=0 ",
            "and tag_auth_status = 2 and tag_value = #{tagCode}",
            "</script>"})
    List<TagTestAccountRelDO> getTestAccountIdsByTag(String tagCode);
}
