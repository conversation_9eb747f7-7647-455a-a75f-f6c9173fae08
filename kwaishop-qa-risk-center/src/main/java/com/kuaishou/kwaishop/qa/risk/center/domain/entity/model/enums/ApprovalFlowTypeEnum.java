package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/8 17:27
 * @注释 dpm,流程枚举
 */

public enum ApprovalFlowTypeEnum {

    UN_KNOW("UN_KNOW",
            "UN_KNOW",
            "未知",
            "默认标题"),

    START("START",
            "START",
            "审批开始",
            "审批开始"),

    END("END",
            "END",
            "审批结束",
            "审批结束");

    /**
     * 测试审批流名称，在测试流程中心中申请的4位
     */
    private String testFlowName;


    /**
     * 审批流程名称，在流程中心申请的四位
     */
    private String flowName;

    /**
     * 审批流程描述
     */
    private String desc;

    /**
     * 标题
     */
    private String title;

    ApprovalFlowTypeEnum(String testFlowName, String flowName, String desc,
                         String title) {
        this.testFlowName = testFlowName;

        this.flowName = flowName;

        this.desc = desc;

        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }





    public String getTestFlowName() {
        return testFlowName;
    }

    public void setTestFlowName(String testFlowName) {
        this.testFlowName = testFlowName;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public static ApprovalFlowTypeEnum fromFlowName(String flowName) {
        for (ApprovalFlowTypeEnum value : ApprovalFlowTypeEnum.values()) {
            if (StringUtils.equals(value.getFlowName(), flowName)) {
                return value;
            }
            if (StringUtils.equals(value.getTestFlowName(), flowName)) {
                return value;
            }
        }
        return UN_KNOW;
    }
}
