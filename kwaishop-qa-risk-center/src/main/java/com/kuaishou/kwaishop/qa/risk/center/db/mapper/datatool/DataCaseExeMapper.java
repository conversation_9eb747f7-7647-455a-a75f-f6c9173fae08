package com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.CaseExecuteDO;


/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2022-12-11
 */

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface DataCaseExeMapper extends BaseMapper<CaseExecuteDO> {
    @Update("update data_case_exe set creator_name = #{operator}, deleted = 1 where id = #{id}")
    int logicDeleted(@Param("operator") String operator, @Param("id") Long id);


    @Select("select * from data_case_exe where  deleted=0 and name like CONCAT('%',#{name},'%') ")
    List<CaseExecuteDO> queryCaseExecuteLikeName(@Param("name") String name);

    @Select("select * from data_case_exe where  deleted=0")
    List<CaseExecuteDO> queryAllCaseExe();



    @Select("select * from data_case_exe  where deleted=0 and id = #{id} ")
    CaseExecuteDO queryCaseExecuteById(@Param("id") Long id);

    @Select("select * from data_case_exe  where deleted=0 and case_id = #{id} ")
    List<CaseExecuteDO> queryCaseExecuteByCaseId(@Param("id") Long id);


}
