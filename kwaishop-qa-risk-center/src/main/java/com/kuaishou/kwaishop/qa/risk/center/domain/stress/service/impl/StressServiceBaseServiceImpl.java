package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.QUERY_SCENARIO_ERROR;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressServiceBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressServiceBaseBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressServiceBaseService;

@Service
public class StressServiceBaseServiceImpl implements StressServiceBaseService {

    @Resource
    private StressServiceBaseDAO serviceBaseDAO;

    @Override
    public PageBO<StressServiceBaseDO> queryPageList(QueryStressServiceBaseBO queryBO) {
        if (queryBO == null) {
            throw new BizException(QUERY_SCENARIO_ERROR);
        }
        return  serviceBaseDAO.queryStressServiceBaseList(queryBO);
    }
}
