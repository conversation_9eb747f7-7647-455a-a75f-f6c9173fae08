package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.StringEnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
public enum MobileCountryCodeEnum {

    CITY_1264("+1264", "+1264地区", UserLoginTypeEnum.CITY_1264_LOGIN),
    CITY_86("+86", "+86地区", UserLoginTypeEnum.CITY_86_LOGIN),
    ;

    private final String type;

    private final String desc;

    private final UserLoginTypeEnum loginType;

    MobileCountryCodeEnum(String type, String desc, UserLoginTypeEnum loginType) {
        this.type = type;
        this.desc = desc;
        this.loginType = loginType;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public UserLoginTypeEnum getLoginType() {
        return loginType;
    }

    public static MobileCountryCodeEnum of(String type) {
        for (MobileCountryCodeEnum entityTypeEnum: values()) {
            if (entityTypeEnum.getType().equals(type)) {
                return entityTypeEnum;
            }
        }
        return null;
    }

    public static List<StringEnumInfo> buildEnumInfo() {
        List<StringEnumInfo> res = new ArrayList<>();
        for (MobileCountryCodeEnum typeEnum: values()) {
            StringEnumInfo enumInfo = StringEnumInfo.newBuilder()
                    .setValue(typeEnum.getType())
                    .setDesc(typeEnum.getDesc())
                    .build();
            res.add(enumInfo);
        }
        return res;
    }
}
