package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ImportResultBO {

    private List<Long> failedUserIdList;

    private List<UserAccountDO> userAccountDOList;
}
