package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressInterfaceBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressInterfaceRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressInterfaceRecordService;

@Service
public class StressInterfaceRecordServiceImpl implements StressInterfaceRecordService {

    @Resource
    private StressInterfaceRecordDAO recordDAO;

    @Resource
    private StressInterfaceBaseDAO baseDAO;

    @Override
    public List<StressInterfaceRecordDO> getStressInterfaceRecordWithBase(Long recordId) {
        List<StressInterfaceRecordDO> list = new ArrayList<>();
        StressInterfaceRecordDO interfaceRecordDO =  recordDAO.queryStressInterfaceRecordById(recordId);
        Long interfaceId = interfaceRecordDO.getInterfaceId();
        list.add(interfaceRecordDO);
        StressInterfaceBaseDO baseDO = baseDAO.queryStressInterfaceBaseByInterfaceId(interfaceId);
        StressInterfaceRecordDO baseRecordDO = StressInterfaceRecordDO.builder()
                .interfaceId(interfaceRecordDO.getInterfaceId())
                .interfaceName(interfaceRecordDO.getInterfaceName())
                .serviceId(interfaceRecordDO.getServiceId())
                .serviceName(interfaceRecordDO.getServiceName())
                .scenarioRecordId(interfaceRecordDO.getScenarioRecordId())
                .scenarioRecordName(interfaceRecordDO.getScenarioRecordName())
                .scenarioId(interfaceRecordDO.getScenarioId())
                .scenarioName(interfaceRecordDO.getScenarioName())
                .avg75(baseDO.getAvg75())
                .avg95(baseDO.getAvg95())
                .avg99(baseDO.getAvg99())
                .avg995(baseDO.getAvg995())
                .avgRT(baseDO.getAvgRT())
                .maxQps(baseDO.getMaxQps())
                .status(1)
                .passPercent(baseDO.getPassPercent())
                .createTime(baseDO.getCreateTime())
                .updateTime(baseDO.getUpdateTime())
                .id(0L)
                .build();
        list.add(baseRecordDO);
        return list;
    }
}
