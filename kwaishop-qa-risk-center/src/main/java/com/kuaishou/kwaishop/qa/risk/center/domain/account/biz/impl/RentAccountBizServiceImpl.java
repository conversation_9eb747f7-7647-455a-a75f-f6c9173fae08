package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_AUTOMATION_NOT_RENT_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_DURATION_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_EXIST_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_EXTEND_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_RENT_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_RETURN_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.RENT_BORROWER_ERROR;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.RentAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.RentAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAuthConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAuthBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.RentalStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.AutoReturnService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.RentAccountService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAccountService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAuthService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.cache.EntityCacheService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.AccountRentalDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ApplyAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ExtendRentRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.PageRentAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageRentRecordsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryRentRecordsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.RentAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ReturnAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.utils.JsonUtils;
import com.kuaishou.kwaishop.qa.risk.center.utils.kim.KimMsg;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-26
 */
@Lazy
@Service
@Slf4j
public class RentAccountBizServiceImpl implements RentAccountBizService {

    @SuppressWarnings("checkstyle:ConstantName")
    private static final Integer hour = 24;

    @Autowired
    private RentAccountService rentAccountService;

    @Autowired
    private RentAccountConvert rentAccountConvert;

    @Autowired
    private UserAccountService userAccountService;

    @Autowired
    private EntityCacheService entityCacheService;

    @Autowired
    private AutoReturnService autoReturnService;

    @Autowired
    private UserAuthService userAuthService;

    @Autowired
    private UserAuthConvert userAuthConvert;

    @Override
    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public void rentAccount(RentAccountRequest request) {
        //根据user_id返回账户info
        checkParams(request);
        List<UserAccountDO> userAccountDOS = userAccountService.queryAccountByUserId(request.getUserId());
        if (userAccountDOS == null || userAccountDOS.isEmpty()) {
            throw new BizException(ACCOUNT_NOT_EXIST_ERROR);
        }
        log.info("rentAccount 根据userid获取账户info, {}", JsonUtils.toJsonArray(userAccountDOS));
        UserAccountDO userAccountDO = userAccountDOS.get(0);
        UserStatusEnum userStatusEnum = UserStatusEnum.of(userAccountDO.getStatus());
        if (userStatusEnum == null
                || userStatusEnum.equals(UserStatusEnum.CAN_NOT_APPLY)
                || userStatusEnum.equals(UserStatusEnum.CAN_NOT_APPLY_AUTOMATION)) {
            throw new BizException(userStatusEnum.equals(UserStatusEnum.CAN_NOT_APPLY)
                    ? ACCOUNT_NOT_RENT_ERROR
                    : ACCOUNT_AUTOMATION_NOT_RENT_ERROR);
        }
        //根据user_id的info，组成rentAccount数据，插入其中。
        RentAccountBO rentAccountBO = rentAccountConvert.buildRentAccountBO(userAccountDO, request);
        log.info("rentAccount rentAccountBO={}", JsonUtils.toJsonString(rentAccountBO));
        //插入租借数据
        rentAccountService.rentAccount(rentAccountBO);
        //更改账户表数据，为不可租借状态
        userAccountService.updateRentStatus(UserStatusEnum.CAN_NOT_APPLY.getCode(), rentAccountBO.getBorrower(), request.getUserId());
        //插入延时队列任务，到期归还

        //发送租借消息
        sendMsg(request.getUserId(), rentAccountBO.getBorrower(), request.getDuration());

    }

    //申领账户，导入租户表
    @Override
    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public void rentAccount(ApplyAccountRequest request, UserAccountDO userDo) {
        //根据user_id返回账户info
        List<UserAccountDO> userAccountDOS = userAccountService.queryAccountByUserId(userDo.getUserId());
        if (userAccountDOS == null || userAccountDOS.isEmpty()) {
            throw new BizException(ACCOUNT_NOT_EXIST_ERROR);
        }
        log.info("rentAccount 根据userid获取账户info, {}", JsonUtils.toJsonArray(userAccountDOS));
        UserAccountDO userAccountDO = userAccountDOS.get(0);
        UserStatusEnum userStatusEnum = UserStatusEnum.of(userAccountDO.getStatus());
        if (userStatusEnum == null
                || userStatusEnum.equals(UserStatusEnum.CAN_NOT_APPLY)
                || userStatusEnum.equals(UserStatusEnum.CAN_NOT_APPLY_AUTOMATION)) {
            throw new BizException(userStatusEnum.equals(UserStatusEnum.CAN_NOT_APPLY)
                    ? ACCOUNT_NOT_RENT_ERROR
                    : ACCOUNT_AUTOMATION_NOT_RENT_ERROR);
        }
        //根据user_id的info，组成rentAccount数据，插入其中。
        RentAccountBO rentAccountBO = rentAccountConvert
                .buildRentAccountBO(userAccountDO, userDo.getUserId(), request.getOperator(), Integer.parseInt(request.getRentalTime()));
        log.info("rentAccount rentAccountBO={}", JsonUtils.toJsonString(rentAccountBO));
        //插入租借数据
        rentAccountService.rentAccount(rentAccountBO);
        //更改账户表数据，为不可租借状态
        userAccountService.updateRentStatus(UserStatusEnum.CAN_NOT_APPLY.getCode(), rentAccountBO.getBorrower(), userDo.getUserId());
        //插入延时队列任务，到期归还

        //发送租借消息
        sendMsg(userDo.getUserId(), request.getOperator(), Integer.parseInt(request.getRentalTime()));

    }

    private void checkParams(RentAccountRequest request) {
        if (request.getDuration() == 0) {
            throw new BizException(ACCOUNT_NOT_DURATION_ERROR);
        }
        if (request.getBorrower().isEmpty()) {
            throw new BizException(RENT_BORROWER_ERROR);
        }
    }

    @Override
    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public void returnAccount(ReturnAccountRequest request) {
        List<RentAccountDO> rentAccountDOS = rentAccountService.queryRentRecords(RentAccountBO.builder()
                .userId(request.getUserId())
                .borrower(request.getBorrower())
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .build());
        if (rentAccountDOS == null || rentAccountDOS.isEmpty()) {
            throw new BizException(ACCOUNT_NOT_RETURN_ERROR);
        }
        // 根据id修改租借表账户表的，数据
        RentAccountBO returnAccountBO = rentAccountConvert.buildReturnAccountBO(rentAccountDOS.get(0));
        rentAccountService.returnAccount(returnAccountBO);
        // 更新用户表
        userAccountService.updateRentStatus(UserStatusEnum.CAN_APPLY.getCode(), request.getBorrower(), rentAccountDOS.get(0).getUserId());
    }

    @Override
    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    public void autoReturnAccount(ReturnAccountRequest request) {
        List<RentAccountDO> rentAccountDOS = rentAccountService.queryRentRecords(RentAccountBO.builder()
                .userId(request.getUserId())
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .build());
        if (rentAccountDOS == null || rentAccountDOS.isEmpty()) {
            //如果没有，说明已经归还了。
            log.info("request={}, 没有扫描到，说明已经归还了", JsonUtils.toJsonString(request));
            return;
        }
        // 根据id修改租借表账户表的，数据
        RentAccountBO returnAccountBO = rentAccountConvert.buildReturnAccountBO(rentAccountDOS.get(0));
        rentAccountService.returnAccount(returnAccountBO);
        // 更新用户表
        userAccountService.updateRentStatus(UserStatusEnum.CAN_APPLY.getCode(), request.getBorrower(), rentAccountDOS.get(0).getUserId());
    }

    @Override
    public List<AccountRentalDTO> queryRentRecords(QueryRentRecordsRequest request) {
        RentAccountBO rentAccountBO = rentAccountConvert.buildQueryRentRecords(request);
        List<RentAccountDO> rentAccountDOS = rentAccountService.queryRentRecords(rentAccountBO);
        return buildRentAccountDTO(rentAccountDOS);
    }

    @Override
    public PageRentAccountDTO queryPageRentRecords(QueryPageRentRecordsRequest request, boolean isDetail) {
        RentAccountBO queryBO = rentAccountConvert.buildQueryPageRentRecords(request);
        PageBO<RentAccountDO> resList = rentAccountService.queryPageRentRecords(queryBO);
        return buildPageRentRecords(resList, isDetail);
    }

    @Override
    public void extendRent(ExtendRentRequest request) {
        // 先要查出具体要延长的user_id
        List<RentAccountDO> rentAccountDOS = rentAccountService.queryRentRecords(RentAccountBO.builder()
                .userId(request.getUserId())
                .borrower(request.getBorrower())
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .build());
        if (rentAccountDOS == null || rentAccountDOS.isEmpty()) {
            throw new BizException(ACCOUNT_NOT_EXTEND_ERROR);
        }

        RentAccountBO rentAccountBO = rentAccountConvert.buildExtendRentRequest(request, rentAccountDOS.get(0));
        // 更改账户表
        rentAccountService.extendRent(rentAccountBO);
    }

    @Override
    public void autoReturn() {
        // 全表扫描
        List<RentAccountDO> rentAccountDOS = rentAccountService.queryRentRecords(RentAccountBO.builder()
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .build());
        if (rentAccountDOS.isEmpty()) {
            log.info("没有需要归还的");
            return;
        }
        long currentTimeMillis = System.currentTimeMillis();
        rentAccountDOS.forEach(rentAccountDO -> {
            if (rentAccountDO.getDueTime() < currentTimeMillis) {
                autoReturnAccount(ReturnAccountRequest.newBuilder()
                        .setBorrower("autoReturn")
                        .setUserId(rentAccountDO.getUserId())
                        .build());
            }
        });
    }

    @Override
    public List<String> queryListByOperator(RentAccountBO build) {
        return rentAccountService.queryListByOperator(build.getBorrower());
    }

    private PageRentAccountDTO buildPageRentRecords(PageBO<RentAccountDO> pageBO, boolean isDetail) {
        return PageRentAccountDTO.newBuilder()
                .setPageNo(pageBO.getPageNo())
                .setPageSize(pageBO.getPageSize())
                .setTotal(pageBO.getTotal())
                .addAllDetails(buildRentalDTOByUserAuth(buildRentAccountDTO(pageBO.getData()), isDetail))
                .build();
    }

    private List<AccountRentalDTO> buildRentAccountDTO(List<RentAccountDO> dataList) {
        List<AccountRentalDTO> accountRentalDTOs = new ArrayList<>();
        Set<Long> entityIds = new HashSet<>();
        dataList.forEach(e -> {
            entityIds.add(e.getTeamId());
            entityIds.add(e.getCenterId());
        });
        Map<Long, EntityDO> entityDOMap = entityCacheService.batchQueryByIds(entityIds);
        dataList.forEach(data -> {
            EntityDO centerDO = entityDOMap.get(data.getCenterId());
            String centerName = centerDO == null ? StringUtils.EMPTY : centerDO.getName();
            EntityDO teamDO = entityDOMap.get(data.getTeamId());
            String teamName = teamDO == null ? StringUtils.EMPTY : teamDO.getName();
            AccountRentalDTO accountRentalDTO = rentAccountConvert.buildDTOByRentAccountDo(data, centerName, teamName);
            accountRentalDTOs.add(accountRentalDTO);
            log.info("psp:{}", ObjectMapperUtils.toJSON(accountRentalDTO));
        });
        return accountRentalDTOs;
    }

    private List<AccountRentalDTO> buildRentalDTOByUserAuth(List<AccountRentalDTO> rentalDTOS, boolean isDetail) {
        List<Long> userIds = rentalDTOS.stream()
                .map(AccountRentalDTO::getUserId)
                .collect(Collectors.toList());
        // 去查
        List<UserAuthDO> userAuthDOS = null;
        if (isDetail) {
            userAuthDOS = userAuthService.queryUserAuthRecords(UserAuthBO.builder()
                    .userIds(userIds)
                    .build());
        } else {
            userAuthDOS = new ArrayList<UserAuthDO>();
        }
        List<UserAccountDO> userAccountDOS = userAccountService.queryList(UserAccountBO.builder()
                .userIds(userIds)
                .build());
        Map<Long, UserAuthDO> map = userAuthDOS.stream()
                .collect(Collectors.toMap(UserAuthDO::getUserId, Function.identity()));
        Map<Long, UserAccountDO> tokenMap = userAccountDOS.stream()
                .collect(Collectors.toMap(UserAccountDO::getUserId, Function.identity()));
        List<AccountRentalDTO> accountRentalDTOS = new ArrayList<>();
        rentalDTOS.forEach(rentalDTO -> {
            accountRentalDTOS.add(userAuthConvert.buildRentalDTOByUserAuthDTO(rentalDTO, map, tokenMap));
        });
        return accountRentalDTOS;
    }

    public static void sendMsg(long uid, String name, int duration) {
        KimMsg kimMsg = new KimMsg();
        int day = duration / hour;
        String description = "账户ID: " + uid
                + "\n申请时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())
                + "\n将于" + day + "天后过期";
        try {
            kimMsg.sendUserTextMessage(name, description);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
