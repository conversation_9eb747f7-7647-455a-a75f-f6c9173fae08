package com.kuaishou.kwaishop.qa.risk.center.domain.stress.biz.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.biz.StressBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressInterfaceBaseBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioRecordBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressServiceBaseBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressInterfaceBaseService;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressInterfaceRecordService;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressInterfaceService;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressScenarioRecordService;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressScenarioService;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressSerRecordService;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressSerService;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressServiceBaseService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.CreateScenarioFromJsonRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageScenarioDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageScenarioRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageStressInterfaceBaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageStressServiceBaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryInterfaceBaseListByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryScenarioRecordsByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryScenariosByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryServiceBaseListByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressInterfaceBaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressInterfaceDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressInterfaceRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressInterfaceWithBaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressScenarioDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressScenarioRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressServiceBaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressServiceRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressServiceWithBaseRequest;

import lombok.extern.slf4j.Slf4j;

@Service
@Lazy
@Slf4j
public class StressBizServiceImpl implements StressBizService {
    @Resource
    private StressScenarioRecordService scenarioRecordService;

    @Resource
    private StressScenarioService scenarioService;

    @Resource
    private StressInterfaceService interfaceService;

    @Resource
    private StressSerService serService;

    @Resource
    private StressSerRecordService serRecordService;

    @Resource
    private StressInterfaceRecordService stressInterfaceRecordService;

    @Resource
    private StressServiceBaseService serviceBaseService;

    @Resource
    private StressInterfaceBaseService interfaceBaseService;

    @Override
    public PageScenarioDTO queryPageScenarioList(QueryScenariosByPageRequest request) {
        QueryStressScenarioBO queryBO = QueryStressScenarioBO.builder().name(request.getScenarioName())
                .pageNo(request.getPageNo()).pageSize(request.getPageSize()).build();
        PageBO<StressScenarioDO> pageBO = scenarioService.queryPageList(queryBO);
        return convertToScenarioPageDTO(pageBO);
    }

    @Override
    public PageScenarioDTO queryPageScenarioWithInterface(QueryScenariosByPageRequest request) {
        QueryStressScenarioBO queryBO = QueryStressScenarioBO.builder().name(request.getScenarioName())
                .pageNo(request.getPageNo()).pageSize(request.getPageSize()).build();
        PageBO<StressScenarioDO> pageBO = scenarioService.queryPageListWithInterface(queryBO);
        return convertToScenarioPageDTO(pageBO);
    }

    @Override
    public PageScenarioRecordDTO queryPageScenarioRecordList(QueryScenarioRecordsByPageRequest request) {
        QueryStressScenarioRecordBO queryBO = QueryStressScenarioRecordBO.builder().pageNo(request.getPageNo())
                .pageSize(request.getPageSize()).scenarioId(request.getScenarioId()).scenarioName(request.getScenarioName()).build();
        PageBO<StressScenarioRecordDO> pageBO = scenarioRecordService.queryPageList(queryBO);
        return convertToScenarioRecordPageDTO(pageBO);
    }

    @Override
    public void batchImportPageStressInterface(QueryScenarioRecordsByPageRequest request) {

    }

    @Override
    public void createStressInterface(QueryScenarioRecordsByPageRequest request) {

    }

    @Override
    public void createStressSerBase(QueryScenarioRecordsByPageRequest request) {

    }

    @Override
    public void updateStressSerBase(QueryScenarioRecordsByPageRequest request) {

    }

    @Override
    public void updateStressInterfaceBase(QueryScenarioRecordsByPageRequest request) {

    }

    @Override
    public void deleteStressInterfaceBase(QueryScenarioRecordsByPageRequest request) {

    }

    @Override
    public void deleteStressSerBase(QueryScenarioRecordsByPageRequest request) {

    }

    @Override
    public List<StressInterfaceRecordDTO> compareStressInterfaceBase(StressInterfaceWithBaseRequest request) {
        List<StressInterfaceRecordDO> list = stressInterfaceRecordService.getStressInterfaceRecordWithBase(request.getId());
        return convertStressInterfaceRecordDTOList(list);
    }

    @Override
    public List<StressServiceRecordDTO> compareStressSerBase(StressServiceWithBaseRequest stressServiceWithBaseRequest) {
        List<StressServiceRecordDO> list = serRecordService.getStressSerRecordWithBase(stressServiceWithBaseRequest.getId());
        return convertStressServiceRecordDTOList(list);
    }


    @Override
    public PageScenarioDTO queryPageSerBaseList(QueryScenariosByPageRequest request) {
        return null;
    }

    @Override
    public PageStressServiceBaseDTO queryServiceBaseListByPage(QueryServiceBaseListByPageRequest queryServiceBaseListByPageRequest) {
        QueryStressServiceBaseBO queryBO = QueryStressServiceBaseBO.builder().serviceName(queryServiceBaseListByPageRequest.getServiceName())
                .pageNo(queryServiceBaseListByPageRequest.getPageNo()).pageSize(queryServiceBaseListByPageRequest.getPageSize()).build();
        PageBO<StressServiceBaseDO> pageBO = serviceBaseService.queryPageList(queryBO);
        return convertPageStressServiceBaseDTO(pageBO);
    }

    @Override
    public PageStressInterfaceBaseDTO queryInterfaceBaseListByPage(QueryInterfaceBaseListByPageRequest queryInterfaceBaseListByPageRequest) {
        QueryStressInterfaceBaseBO queryBO = QueryStressInterfaceBaseBO.builder()
                .interfaceName(queryInterfaceBaseListByPageRequest.getInterfaceName())
                .pageNo(queryInterfaceBaseListByPageRequest.getPageNo()).pageSize(queryInterfaceBaseListByPageRequest.getPageSize()).build();
        PageBO<StressInterfaceBaseDO> pageBO = interfaceBaseService.queryPageList(queryBO);
        return convertPageStressInterfaceBaseDTO(pageBO);
    }


    @Override
    public PageScenarioDTO queryPageInterfaceRecordListWithBase(QueryScenariosByPageRequest request, Long interfaceId) {
        return null;
    }

    @Override
    public PageScenarioDTO queryPageServiceRecordListWithBase(QueryScenariosByPageRequest request, Long interfaceId) {
        return null;
    }

    @Override
    public Boolean createScenarioFromJson(CreateScenarioFromJsonRequest createScenarioFromJsonRequest) {
        String json = createScenarioFromJsonRequest.getData();
        Boolean ret = scenarioService.createScenarioFromImport(json);
        return ret;
    }

    @Override
    public StressScenarioRecordDTO getScenarioRecordDetail(Long id) {
        StressScenarioRecordDO scenarioRecordDO = scenarioRecordService.getScenarioRecordWithInterface(id);
        return convertToScenarioRecordDTO(scenarioRecordDO);
    }


    private PageScenarioDTO convertToScenarioPageDTO(PageBO<StressScenarioDO> pageBO) {
        if (pageBO.getData() == null) {
            return PageScenarioDTO.newBuilder()
                    .setPageNo(pageBO.getPageNo())
                    .setPageSize(pageBO.getPageSize())
                    .addAllScenarioList(new ArrayList<>())
                    .setTotal(pageBO.getTotal())
                    .build();
        }
        return PageScenarioDTO.newBuilder()
                .setPageNo(pageBO.getPageNo())
                .setPageSize(pageBO.getPageSize())
                .addAllScenarioList(pageBO.getData().stream().map(this::convertToScenarioDTO).collect(Collectors.toList()))
                .setTotal(pageBO.getTotal())
                .build();
    }

    private PageStressServiceBaseDTO convertPageStressServiceBaseDTO(PageBO<StressServiceBaseDO> pageBO) {
        return PageStressServiceBaseDTO.newBuilder()
                .setPageNo(pageBO.getPageNo())
                .setPageSize(pageBO.getPageSize())
                .addAllStressServiceBaseList(pageBO.getData().stream().map(this::convertToServiceBaseDTO).collect(Collectors.toList()))
                .setTotal(pageBO.getTotal())
                .build();
    }

    private List<StressServiceRecordDTO> convertStressServiceRecordDTOList(List<StressServiceRecordDO> list) {
        List<StressServiceRecordDTO> dtos = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            dtos.add(convertStressServiceRecordDTO(list.get(i)));
        }
        return dtos;
    }

    private StressServiceRecordDTO convertStressServiceRecordDTO(StressServiceRecordDO recordDO) {
        return StressServiceRecordDTO.newBuilder()
                .setAvgCpu(recordDO.getAvgCpu())
                .setAvgDisk(recordDO.getAvgDisk())
                .setAvgExceptions(recordDO.getAvgExceptions())
                .setAvgRam(recordDO.getAvgRam())
                .setMaxCpu(recordDO.getMaxCpu())
                .setMaxRam(recordDO.getMaxRam())
                .setMaxDisk(recordDO.getMaxDisk())
                .setAvgRisk(recordDO.getAvgRisk())
                .setMaxExceptions(recordDO.getMaxExceptions())
                .setStatus(recordDO.getStatus())
                .setMaxThreadPools(recordDO.getMaxThreadPools())
                .setServiceId(recordDO.getServiceId())
                .setServiceName(recordDO.getServiceName())
                .setScenarioId(recordDO.getScenarioId())
                .setScenarioName(recordDO.getScenarioName())
                .setScenarioRecordId(recordDO.getScenarioRecordId())
                .setScenarioRecordName(recordDO.getScenarioRecordName())
                .setId(recordDO.getId())
                .build();
    }

    private List<StressInterfaceRecordDTO> convertStressInterfaceRecordDTOList(List<StressInterfaceRecordDO> list) {
        List<StressInterfaceRecordDTO> dtos = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            dtos.add(convertStressInterfaceRecordDTO(list.get(i)));
        }
        return dtos;
    }

    private StressInterfaceRecordDTO convertStressInterfaceRecordDTO(StressInterfaceRecordDO recordDO) {
        return StressInterfaceRecordDTO.newBuilder()
                .setAvg75(recordDO.getAvg75())
                .setAvg95(recordDO.getAvg95())
                .setAvg99(recordDO.getAvg99())
                .setServiceId(recordDO.getServiceId())
                .setInterfaceId(recordDO.getInterfaceId())
                .setPassPercent(recordDO.getPassPercent())
                .setMaxQps(recordDO.getMaxQps())
                .setAvgRt(recordDO.getAvgRT())
                .setAvg995(recordDO.getAvg995())
                .setServiceName(recordDO.getServiceName())
                .setId(recordDO.getId())
                .setStatus(recordDO.getStatus())
                .setServiceId(recordDO.getServiceId())
                .setScenarioId(recordDO.getScenarioId())
                .setScenarioName(recordDO.getScenarioName())
                .setScenarioRecordId(recordDO.getScenarioRecordId())
                .setScenarioRecordName(recordDO.getScenarioRecordName())
                .setInterfaceName(recordDO.getInterfaceName())
                .setCreateTime(recordDO.getCreateTime())
                .setUpdateTime(recordDO.getUpdateTime())
                .build();
    }

    private PageStressInterfaceBaseDTO convertPageStressInterfaceBaseDTO(PageBO<StressInterfaceBaseDO> pageBO) {
        return PageStressInterfaceBaseDTO.newBuilder()
                .setPageNo(pageBO.getPageNo())
                .setPageSize(pageBO.getPageSize())
                .addAllStressInterfaceBaseList(pageBO.getData().stream().map(this::convertToInterfaceBaseDTO).collect(Collectors.toList()))
                .setTotal(pageBO.getTotal())
                .build();
    }

    private StressScenarioDTO convertToScenarioDTO(StressScenarioDO stressScenarioDO) {
        return StressScenarioDTO.newBuilder()
                .setCreateTime(stressScenarioDO.getCreateTime())
                .setUpdateTime(stressScenarioDO.getUpdateTime())
                .setName(stressScenarioDO.getName())
                .setOutId(stressScenarioDO.getOutId())
                .setOutUrl(stressScenarioDO.getOutUrl())
                .setOwnerId(stressScenarioDO.getOwnerId())
                .setStatus(stressScenarioDO.getStatus())
                .setId(stressScenarioDO.getId())
                .addAllInterfaceList(stressScenarioDO
                        .getStressInterfaces().stream().map(this::convertToStressInterfaceDTO)
                        .collect(Collectors.toList()))
                .build();
    }

    private StressServiceBaseDTO convertToServiceBaseDTO(StressServiceBaseDO sDO) {
        return StressServiceBaseDTO.newBuilder()
                .setServiceId(sDO.getServiceId())
                .setServiceName(sDO.getServiceName())
                .setId(sDO.getId())
                .setAvgCpu(sDO.getAvgCpu())
                .setAvgDisk(sDO.getAvgDisk())
                .setMaxCpu(sDO.getMaxCpu())
                .setMaxThreadPools(sDO.getMaxThreadPools())
                .setMaxRisk(sDO.getMaxRisk())
                .setMaxDisk(sDO.getMaxDisk())
                .setAvgExceptions(sDO.getAvgExceptions())
                .setMaxExceptions(sDO.getMaxExceptions())
                .setScenarioId(sDO.getScenarioId())
                .setScenarioName(sDO.getScenarioName())
                .setStatus(sDO.getStatus())
                .setCreateTime(sDO.getCreateTime())
                .setUpdateTime(sDO.getUpdateTime())
                .setAvgThreadPools(sDO.getAvgThreadPools()).build();
    }

    private StressInterfaceBaseDTO convertToInterfaceBaseDTO(StressInterfaceBaseDO sDO) {
        return StressInterfaceBaseDTO.newBuilder()
                .setInterfaceName(sDO.getInterfaceName())
                .setInterfaceId(sDO.getInterfaceId())
                .setId(sDO.getId())
                .setStatus(sDO.getStatus())
                .setServiceId(sDO.getServiceId())
                .setAvg995(sDO.getAvg995())
                .setAvg95(sDO.getAvg95())
                .setAvgRt(sDO.getAvgRT())
                .setAvg99(sDO.getAvg99())
                .setAvg75(sDO.getAvg75())
                .build();
    }

    private PageScenarioRecordDTO convertToScenarioRecordPageDTO(PageBO<StressScenarioRecordDO> pageBO) {
        return PageScenarioRecordDTO.newBuilder()
                .setPageNo(pageBO.getPageNo())
                .setPageSize(pageBO.getPageSize())
                .addAllScenarioRecordList(pageBO.getData().stream().map(this::convertToScenarioRecordDTO).collect(Collectors.toList()))
                .setTotal(pageBO.getTotal())
                .build();
    }

    private StressScenarioRecordDTO convertToScenarioRecordDTO(StressScenarioRecordDO stressScenarioRecordDO) {
        return StressScenarioRecordDTO.newBuilder()
                .setCreateTime(stressScenarioRecordDO.getCreateTime())
                .setUpdateTime(stressScenarioRecordDO.getUpdateTime())
                .setStatus(stressScenarioRecordDO.getStatus())
                .setScenarioId(stressScenarioRecordDO.getScenarioId())
                .setId(stressScenarioRecordDO.getId())
                .setFromTime(stressScenarioRecordDO.getFromTime())
                .setToTime(stressScenarioRecordDO.getToTime())
                .setSumBase(stressScenarioRecordDO.getSumBase())
                .setScenarioName(stressScenarioRecordDO.getScenarioName())
                .addAllInterfaceRecordList(stressScenarioRecordDO.getInterfaceRecords().stream().map(this::convertStressInterfaceRecordDTO)
                        .collect(Collectors.toList()))
                .addAllServiceRecordList(stressScenarioRecordDO.getServiceRecords().stream().map(this::convertStressServiceRecordDTO)
                        .collect(Collectors.toList()))
                .build();
    }



    private StressInterfaceDTO convertToStressInterfaceDTO(StressInterfaceDO siDO) {
        return StressInterfaceDTO.newBuilder()
                .setCreateTime(siDO.getCreateTime())
                .setUpdateTime(siDO.getUpdateTime())
                .setId(siDO.getId())
                .setStatus(siDO.getStatus())
                .setServiceId(siDO.getServiceId())
                .setServiceName(siDO.getServiceName())
                .setInterfaceName(siDO.getInterfaceName())
                .setInterfaceDesc(siDO.getInterfaceDesc())
                .build();
    }
}