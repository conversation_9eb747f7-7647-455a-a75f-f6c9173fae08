package com.kuaishou.kwaishop.qa.risk.center.domain.entity.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDataDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDataBO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-30
 */
public interface EntityDataService {

    void createEntityData(EntityDataBO entityDataBO);

    EntityDataDO queryEntityData(EntityDataBO entityDataBO);

    List<EntityDataDO> queryEntityDataList(EntityDataBO entityDataBO);
}
