package com.kuaishou.kwaishop.qa.risk.center.db.mapper.common;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.common.CommonLogRecordDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-24
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface CommonLogRecordMapper extends BaseMapper<CommonLogRecordDO> {

    @Select("select * from common_log_record where log_type = #{logType}"
            + " and dt = #{dt} and id > #{cursor} order by id asc limit #{limit}")
    List<CommonLogRecordDO> queryRecordByCursor(@Param("logType") Integer logType,
            @Param("dt") String dt, @Param("cursor") Long cursor, @Param("limit") Integer limit);
}
