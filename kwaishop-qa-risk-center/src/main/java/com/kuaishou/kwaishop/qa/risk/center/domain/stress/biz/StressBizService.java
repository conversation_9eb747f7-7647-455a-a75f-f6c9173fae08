package com.kuaishou.kwaishop.qa.risk.center.domain.stress.biz;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.CreateScenarioFromJsonRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageScenarioDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageScenarioRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageStressInterfaceBaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.PageStressServiceBaseDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryInterfaceBaseListByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryScenarioRecordsByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryScenariosByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.QueryServiceBaseListByPageRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressInterfaceRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressInterfaceWithBaseRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressScenarioRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressServiceRecordDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.stress.StressServiceWithBaseRequest;


public interface StressBizService {
    /**
     * 分页查找场景列表
     * @param request
     * @return
     */
    PageScenarioDTO queryPageScenarioList(QueryScenariosByPageRequest request);

    /**
     * 分页查找场景列表
     * @param request
     * @return
     */
    PageScenarioDTO queryPageScenarioWithInterface(QueryScenariosByPageRequest request);

    /**
     * 分页查找场景压测记录列表
     * @param request
     * @return
     */
    PageScenarioRecordDTO queryPageScenarioRecordList(QueryScenarioRecordsByPageRequest request);


    /**
     * 批量导入用例
     * @param request
     * @return
     */
    void batchImportPageStressInterface(QueryScenarioRecordsByPageRequest request);

    /**
     * 单个创建用例
     * @param request
     * @return
     */
    void createStressInterface(QueryScenarioRecordsByPageRequest request);

    /**
     * 设置
     * @param request
     */
    void createStressSerBase(QueryScenarioRecordsByPageRequest request);

    /**
     * 设置
     * @param request
     */
    void updateStressSerBase(QueryScenarioRecordsByPageRequest request);

    /**
     * 设置
     * @param request
     */
    void updateStressInterfaceBase(QueryScenarioRecordsByPageRequest request);


    /**
     * 设置
     * @param request
     */
    void deleteStressInterfaceBase(QueryScenarioRecordsByPageRequest request);

    /**
     * 设置
     * @param request
     */
    void deleteStressSerBase(QueryScenarioRecordsByPageRequest request);


    /**
     * 设置
     * @param request
     */
    List<StressInterfaceRecordDTO> compareStressInterfaceBase(StressInterfaceWithBaseRequest request);

    /**
     * 设置
     * @param request
     */
    List<StressServiceRecordDTO> compareStressSerBase(StressServiceWithBaseRequest stressServiceWithBaseRequest);

    /**
     * 分页查找场景列表
     * @param request
     * @return
     */
    PageScenarioDTO queryPageSerBaseList(QueryScenariosByPageRequest request);

    /**
     * 分页查找服务基线列表
     * @param queryServiceBaseListByPageRequest
     * @return
     */
    PageStressServiceBaseDTO queryServiceBaseListByPage(QueryServiceBaseListByPageRequest queryServiceBaseListByPageRequest);

    /**
     * 分页查找服务基线列表
     * @param queryInterfaceBaseListByPageRequest
     * @return
     */

    PageStressInterfaceBaseDTO queryInterfaceBaseListByPage(QueryInterfaceBaseListByPageRequest queryInterfaceBaseListByPageRequest);

    /**
     * 分页查找某个接口的压测记录列表
     * @param request
     * @return
     */
    PageScenarioDTO queryPageInterfaceRecordListWithBase(QueryScenariosByPageRequest request, Long interfaceId);

    /**
     * 分页查找某个接口的压测记录列表
     * @param request
     * @return
     */
    PageScenarioDTO queryPageServiceRecordListWithBase(QueryScenariosByPageRequest request, Long interfaceId);

    Boolean createScenarioFromJson(CreateScenarioFromJsonRequest createScenarioFromJsonRequest);

    StressScenarioRecordDTO getScenarioRecordDetail(Long id);
}
