package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
public enum EntityTypeEnum {
    UNKNOWN(0, "未知类型"),
    DEFAULT(1, "通用默认类型"),
    CENTER_TYPE(5, "质量中心类型"),
    TEAM_TYPE(10, "团队类型"),
    BUSINESS_TYPE(11, "业务域类型"),
    MEMBER_TYPE(15, "成员类型"),
    ;

    private final Integer code;

    private final String desc;

    EntityTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static EntityTypeEnum of(Integer code) {
        for (EntityTypeEnum entityTypeEnum: values()) {
            if (entityTypeEnum.getCode().equals(code) && entityTypeEnum.getCode() > 0) {
                return entityTypeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (EntityTypeEnum typeEnum: values()) {
            if (typeEnum.getCode() > 0) {
                EnumInfo enumInfo = EnumInfo.newBuilder()
                        .setValue(typeEnum.getCode())
                        .setDesc(typeEnum.getDesc())
                        .build();
                res.add(enumInfo);
            }
        }
        return res;
    }
}
