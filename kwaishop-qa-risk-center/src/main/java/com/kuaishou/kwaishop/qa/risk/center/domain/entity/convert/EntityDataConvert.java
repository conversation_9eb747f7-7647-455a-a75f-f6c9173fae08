package com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert;

import java.util.List;
import java.util.Map;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDataDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDataBO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.EntityDataDTO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-30
 */
public interface EntityDataConvert {

    EntityDataDO buildCreateDO(EntityDataBO entityDataBO);

    EntityDataBO buildCreateBO(Long entityId, Integer entityType, Integer dateType, String dt, String dataInfo);

    List<EntityDataDTO> buildEntityDataDTO(List<EntityDataDO> entityDataDOS, Map<Long, EntityDO> entityDOMap);

    EntityDataDTO buildEntityDataDTO(EntityDataDO entityDataDO, Map<Long, EntityDO> entityDOMap);
}
