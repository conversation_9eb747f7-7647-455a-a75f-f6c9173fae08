package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.AddTagMetaInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.AddTagsToTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.CreateTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.DisableAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.DisableTagRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.GetAccountTagsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.GetTagMetaInfosRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.PageTestAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.PageTestUserRentAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyRentTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.RemoveTagsToSubjectRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.RentTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.ReturnTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.TagBaseInfoDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.TestAccountDTO;

public interface AccountTagManageBizService {

    List<TagBaseInfoDTO> getTagMetaInfos(GetTagMetaInfosRequest request);

    void addTagMetaInfo(AddTagMetaInfoRequest request);

    void addTagsToTestAccount(AddTagsToTestAccountRequest request);

    void removeTagsFromTestAccount(RemoveTagsToSubjectRequest request);

    void createTestAccount(CreateTestAccountRequest request);

    void rentTestAccount(RentTestAccountRequest request);

    TestAccountDTO queryTestAccount(QueryTestAccountRequest request);

    void disableAccount(DisableAccountRequest request);

    List<TagBaseInfoDTO> getAccountTags(GetAccountTagsRequest request);

    /**
     * 查询账户池的账号*
     * @param request
     * @return
     */
    PageTestAccountDTO queryTestAccounts(QueryTestAccountsRequest request);

    /**
     * 查询我拥有的账号*
     * @param request
     * @return
     */
    PageTestAccountDTO queryMyTestAccounts(QueryMyTestAccountsRequest request);

    /**
     * 查询我所有租借的账号
     * @param request
     * @return
     */
    PageTestUserRentAccountDTO queryMyRentTestAccounts(QueryMyRentTestAccountsRequest request);

    /**
     * 查询我所有租借的账号并搜索出tag
     * @param request
     * @return
     */
    PageTestUserRentAccountDTO queryMyRentTestAccountsWithTags(QueryMyRentTestAccountsRequest request);

    /**
     * 查询账户池的账号*
     * @param request
     * @return
     */
    PageTestAccountDTO queryTestAccountsWithTags(QueryTestAccountsRequest request);

    /**
     * 查询我拥有的账号*
     * @param request
     * @return
     */
    PageTestAccountDTO queryMyTestAccountsWithTags(QueryMyTestAccountsRequest request);

    void returnTestAccount();


    void returnTestAccount(ReturnTestAccountRequest request);

    void autoReturnAccount();

    void deleteTag(DisableTagRequest request);
}
