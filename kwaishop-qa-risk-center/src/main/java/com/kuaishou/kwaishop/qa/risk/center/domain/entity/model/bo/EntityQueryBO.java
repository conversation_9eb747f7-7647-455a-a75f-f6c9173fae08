package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo;

import java.util.Collection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-09
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EntityQueryBO {

    private Long id;
    /**
     * 实体类型
     */
    private Integer entityType;
    /**
     * 实体id
     */
    private Long entityId;
    /**
     * 名称查询
     */
    private String nameEq;
    /**
     * 名称查询
     */
    private String nameLike;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 操作人
     */
    private String operator;

    /**
     * 负责人查询
     */
    private String leader;

    /**
     * 名称S
     */
    private Collection<String> names;

    /**
     * 实体ids
     */
    private Collection<Long> entityIds;

    /**
     * 审批单团队id
     */
    private String approveTeamId;

    /**
     * 负责人s
     */
    private Collection<String> leaders;
    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 页容量
     */
    private Integer pageSize;
}
