package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserDbForDumpDO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetEsDataRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetEsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.GetTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.KafkaRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryEsResponse;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAccountDumpRequest;

public interface UserDumpBizService {

    void insertDump(UserAccountDumpRequest userDbForDumpDO);


    UserDbForDumpDO getDumpUser(GetTestAccountRequest userAccountDumpRequest);


    QueryEsResponse getFromEs(GetEsRequest getEsRequest);

    QueryEsResponse getFromEsWithData(GetEsDataRequest getEsRequest);

    void sendKafkaMsg(KafkaRequest request);
}
