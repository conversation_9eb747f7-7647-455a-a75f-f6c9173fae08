package com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.impl;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.gson.JsonObject;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAuthConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAuthBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAuthService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.AccountRentalDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.QueryPageUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.UserAuthDTO;
import com.kuaishou.kwaishop.qa.risk.center.utils.JsonUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-23
 */
@Component
public class UserAuthConvertImpl implements UserAuthConvert {

    @Autowired
    private UserAuthService userAuthService;

    @Override
    public UserAuthDO buildUserAuthDOByLego(UserAccountDO userAccountDO) {
        return null;
    }

    @Override
    public UserAuthDO parseUserAuth(String userAuthResultsString) {
        JsonObject userAuthResults = JsonUtils.stringToJson(userAuthResultsString, JsonObject.class);
        UserAuthDO userAuthDO = new UserAuthDO();
        userAuthDO.setMerchantPermission(covert(userAuthResults.get("是否具有商家权限").getAsBoolean()));
        JsonObject bitAll = userAuthResults.getAsJsonObject("所有bit位");
        userAuthDO.setCelebrityPermission(covert(bitAll.get("推广达人").getAsBoolean()));
        userAuthDO.setDistributorPermission(covert(bitAll.get("分销商家").getAsBoolean()));
        userAuthDO.setRecruitingLeader(covert(bitAll.get("快赚客").getAsBoolean()));
        if (userAuthResults.has("商家信息")) {
            JsonObject sellerInfos = userAuthResults.getAsJsonObject("商家信息");
            userAuthDO.setStoreName(sellerInfos.get("shopName").getAsString());
            userAuthDO.setStoreType(sellerInfos.get("shopTypeDesc").getAsString());
        }
        return userAuthDO;
    }

    @Override
    public AccountRentalDTO buildRentalDTOByUserAuthDTO(AccountRentalDTO rentalDTO, Map<Long, UserAuthDO> map, Map<Long, UserAccountDO> tokenMap) {
        if (rentalDTO == null) {
            throw new IllegalArgumentException("userAccountDTO cannot be null");
        }
        Long userId = rentalDTO.getUserId();
        if (map == null || !map.containsKey(userId)) {
            return AccountRentalDTO.newBuilder(rentalDTO)
                    .setUserAuthDto(UserAuthDTO.newBuilder().build())
                    .build();
        }

        UserAuthDO userAuthDO = map.get(userId);
        UserAccountDO userAccountDO = tokenMap.get(userId);

        return AccountRentalDTO.newBuilder(rentalDTO)
                .setUserAuthDto(buildUserAuthDTO(userAuthDO))
                .setToken(userAccountDO.getExt() == null ? "" : userAccountDO.getExt())
                .build();
    }


    /**
     * 组装权限内容的dto
     * @param userAuthDO
     * @return
     */
    @Override
    public UserAuthDTO buildUserAuthDTO(UserAuthDO userAuthDO) {
        return UserAuthDTO.newBuilder()
                .setDeposit(cover(userAuthDO.getDeposit()))
                .setDistributorPermission(cover(userAuthDO.getDistributorPermission()))
                .setMerchantPermission(cover(userAuthDO.getMerchantPermission()))
                .setLeaderPermission(cover(userAuthDO.getRecruitingLeader()))
                .setPromoterPermission(cover(userAuthDO.getCelebrityPermission()))
                .setIsSeller(cover(userAuthDO.getIsSeller()))
                .setStoreName(userAuthDO.getStoreName() == null ? "" : userAuthDO.getStoreName())
                .setStoreType(userAuthDO.getStoreType() == null ? "" : userAuthDO.getStoreType())
                .setComment(userAuthDO.getComment() == null ? "" : userAuthDO.getComment())
                .setPromoterScore(judgeScore(userAuthDO.getPromoterScore()) ? "90天内分享的商品有效订单不足30笔，暂无法统计带货口碑分"
                        : userAuthDO.getPromoterScore())
                .setShopScore(judgeScore(userAuthDO.getShopScore()) ? "90天内支付金额大于2元的自建订单不足30笔，暂无法统计店铺体验分" : userAuthDO.getShopScore())
                .setShopRating(userAuthDO.getShopRating())
                .setSellerName(userAuthDO.getSellerName())
                .build();
    }

    public boolean judgeScore(String score) {
        return score.equals("0.0") || score.equals("0");
    }

    @Override
    public UserAuthBO buildUserAuthBOByRequest(QueryPageUserAccountRequest request) {
        return UserAuthBO.builder()
                .celebrityPermission(request.getPromoterPermission())
                .distributorPermission(request.getDistributorPermission())
                .merchantPermission(request.getMerchantPermission())
                .recruitingLeader(request.getLeaderPermission())
                .storeType(request.getShopType())
                .build();
    }

    public boolean cover(Integer integer) {
        return integer == 1;
    }



    /**
     * 用来把true转成1，false转成0
     */
    public Integer covert(Boolean b) {
        if (b == null) {
            return 0;
        }
        return b.equals(true) ? 1 : 0;
    }

//    public UserAuthDO parseUserAuth(JsonObject userAuthResults) {
//        UserAuthDO userAuthDO = new UserAuthDO();
//        userAuthDO.setMerchantPermission(covert(userAuthResults.get("是否具有商家权限").getAsBoolean()));
//        JsonObject bitAll = userAuthResults.getAsJsonObject("所有bit位");
//        userAuthDO.setCelebrityPermission(covert(bitAll.get("推广达人").getAsBoolean()));
//        userAuthDO.setDistributorPermission(covert(bitAll.get("分销商家").getAsBoolean()));
//        userAuthDO.setRecruitingLeader(covert(bitAll.get("快赚客").getAsBoolean()));
//        if (userAuthResults.has("商家信息")) {
//            JsonObject sellerInfos = userAuthResults.getAsJsonObject("商家信息");
//            userAuthDO.setStoreName(sellerInfos.get("shopName").getAsString());
//            userAuthDO.setStoreType(sellerInfos.get("shopTypeDesc").getAsString());
//        }
//        return userAuthDO;
//    }
//
//    /**
//     * 用来把true转成1，false转成0
//     */
//    public Integer covert(Boolean b) {
//        if (b == null) {
//            return 0;
//        }
//        return b.equals(true) ? 1 : 0;
//    }
}
