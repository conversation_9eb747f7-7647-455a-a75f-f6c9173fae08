package com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioInterfaceRelDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StressScenarioInterfaceRelMapper extends BaseMapper<StressScenarioInterfaceRelDO> {

    @Select({"<script>", "SELECT * FROM stress_scenario_interface_rel  WHERE 1=1 ",
            "and scenario_id in ",
            "<foreach item='item' collection='scenarioIDs' open='(' separator=',' close=')'>",
            "#{item}",
            "</foreach>",
            "</script>"})
    List<StressScenarioInterfaceRelDO> getByInterfaceListByScenarios(List<Long> scenarioIDs);

    @Select({"<script>", "SELECT * FROM stress_scenario_interface_rel  WHERE 1=1 ",
            "and scenario_id = #{scenarioId} and interface_id = #{interfaceId} limit 0,1",
            "</script>"})
    StressScenarioInterfaceRelDO getRelById(Long scenarioId, Long interfaceId);
}
