package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service;

import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressScenarioRecordBO;

public interface StressSerRecordService {
//    PageBO<StressServiceRecordDO> queryPageList(QueryStressServiceRecordBO queryBO);
    int createStressServiceRecord(StressScenarioRecordBO recordBO);
    StressScenarioRecordDO getStressScenarioRecord();
    List<StressServiceRecordDO> getStressSerRecordWithBase(Long serviceId);
}
