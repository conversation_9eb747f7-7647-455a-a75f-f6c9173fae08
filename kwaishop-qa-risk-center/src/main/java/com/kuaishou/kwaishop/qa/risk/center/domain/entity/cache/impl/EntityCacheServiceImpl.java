package com.kuaishou.kwaishop.qa.risk.center.domain.entity.cache.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskLongConfigKey.entityLoadingCacheExpireTime;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskLongConfigKey.entityLoadingCacheRefreshTime;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskLongConfigKey.maxLoadingCacheCount;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.github.phantomthief.util.MoreStreams;
import com.google.common.cache.CacheLoader.InvalidCacheLoadException;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.kuaishou.framework.concurrent.AsyncReloadCacheLoader;
import com.kuaishou.infra.framework.common.util.KsCacheBuilder;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.EntityDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.cache.EntityCacheService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-12-01
 */
@Service
@Slf4j
@Lazy
public class EntityCacheServiceImpl implements EntityCacheService {

    @Autowired
    private EntityDAO entityDAO;


    private final LoadingCache<Integer, List<EntityDO>> entityDOByEntityTypeLoadingCache =
            KsCacheBuilder.newBuilder()
                    .maximumSize(maxLoadingCacheCount.get())
                    .refreshAfterWrite(() -> Duration.ofSeconds(entityLoadingCacheRefreshTime.get()))
                    .expireAfterAccess(() -> Duration.ofSeconds(entityLoadingCacheExpireTime.get()))
                    .concurrencyLevel(20)
                    .enablePerf("qa.risk.local.cache.entityDOByEntityTypeLoadingCache")
                    .build(new AsyncReloadCacheLoader<Integer, List<EntityDO>>() {
                        @Override
                        public List<EntityDO> load(Integer entityType) {
                            return entityDAO.queryByEntityType(entityType);
                        }
                    });

    private final LoadingCache<Long, Optional<EntityDO>> entityDOByIdsLoadingCache =
            KsCacheBuilder.newBuilder()
                    .maximumSize(maxLoadingCacheCount.get())
                    .refreshAfterWrite(() -> Duration.ofSeconds(entityLoadingCacheRefreshTime.get()))
                    .expireAfterAccess(() -> Duration.ofSeconds(entityLoadingCacheExpireTime.get()))
                    .concurrencyLevel(20)
                    .enablePerf("qa.risk.local.cache.entityDOByIdsLoadingCache")
                    .build(new AsyncReloadCacheLoader<Long, Optional<EntityDO>>() {
                        @Override
                        public Optional<EntityDO> load(Long id) {
                            return Optional.ofNullable(entityDAO.queryById(id));
                        }

                        @Override
                        public Map<Long, Optional<EntityDO>> loadAll(Iterable<? extends Long> keys) {
                            List<Long> ids = MoreStreams.toStream(keys).collect(Collectors.toList());
                            Map<Long, EntityDO> resMap =  entityDAO.batchQueryByIds(ids);
                            return ids.stream().collect(Collectors.toMap(e -> e, e -> Optional.ofNullable(resMap.get(e))));
                        }
                    });


    @Override
    public List<EntityDO> queryByEntityType(Integer entityType) {
        try {
            return entityDOByEntityTypeLoadingCache.get(entityType);
        } catch (ExecutionException | InvalidCacheLoadException e) {
            log.error("[EntityCacheServiceImpl] queryByEntityType error", e);
            return new ArrayList<>();
        }
    }

    @Override
    public EntityDO queryById(Long id) {
        try {
            return entityDOByIdsLoadingCache.get(id).orElse(null);
        } catch (ExecutionException | InvalidCacheLoadException e) {
            log.error("[EntityCacheServiceImpl] queryById error", e);
            return null;
        }
    }

    @Override
    public Map<Long, EntityDO> batchQueryByIds(Collection<Long> ids) {
        Map<Long, EntityDO> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ids)) {
            return resultMap;
        }
        try {
            Map<Long, Optional<EntityDO>> entityMap = entityDOByIdsLoadingCache.getAll(ids);
            if (MapUtils.isEmpty(entityMap)) {
                return resultMap;
            }
            entityMap.forEach((id, entity) -> entity.ifPresent(e -> resultMap.put(id, e)));
            return resultMap;
        } catch (ExecutionException | InvalidCacheLoadException e) {
            log.error("[EntityCacheServiceImpl] queryById error", e);
            return Maps.newHashMap();
        }
    }

}
