package com.kuaishou.kwaishop.qa.risk.center.db.mapper.account;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAuthDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-22
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface UserAuthMapper extends BaseMapper<UserAuthDO> {


}
