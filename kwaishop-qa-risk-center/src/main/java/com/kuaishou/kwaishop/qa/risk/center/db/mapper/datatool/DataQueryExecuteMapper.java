package com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataQueryDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Create on 2022-11-14
 */
@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface DataQueryExecuteMapper extends BaseMapper<DataQueryDO> {
    @Update("update data_query_exe set creator_name = #{operator}, deleted = 1 where id = #{id}")
    int logicDeleted(@Param("operator") String operator, @Param("id") Long id);

    // 增量更新查询日志内容
    @Update("<script>"
            + "update data_query_exe "
            + "set "
            + "log = CONCAT_WS('【分隔符】', log, #{log}) "
            + "where "
            + "id = #{id}"
            + "</script>")
    void updateLog(@Param("id") Long id, @Param("log") String log);

    @Update("<script>"
            + "update data_query_exe "
            + "set "
            + "log = '' "
            + "where "
            + "id = #{id}"
            + "</script>")
    void clearLog(@Param("id") Long id);

    @Update("<script>"
            + "update data_query_exe "
            + "set "
            + "result = #{result} "
            + "where "
            + "id = #{id}"
            + "</script>")
    void updateResult(@Param("id") Long id, @Param("result") String result);

    @Update("<script>"
            + "update data_query_exe "
            + "set "
            + "status =  #{status} "
            + "where "
            + "id = #{id}"
            + "</script>")
    void updateStatus(@Param("id") Long id, @Param("status") Integer status);

    @Select("select id from data_query_exe where deleted=0 and data_source_id = #{data_source_id} order by update_time desc limit 1 ")
    Long queryIdByDataSourceId(@Param("data_source_id") Long dataSourceId);

    @Select("select * from data_query_exe where deleted=0 and data_source_id = #{data_source_id} order by update_time desc limit 1 ")
    DataQueryDO queryResultByDataSourceId(@Param("data_source_id") Long dataSourceId);

}
