package com.kuaishou.kwaishop.qa.risk.center.task.rocketmq;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.client.config.mq.KwaishopQaRiskMqTopic.kwaishop_qa_risk_log_mq;
import static com.kuaishou.kwaishop.qa.risk.center.task.rocketmq.group.KwaishopQaRiskMqConsumer.c_kwaishop_qa_risk_log_mq;

import javax.annotation.Nonnull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.infra.framework.mq.ConsumeContext;
import com.kuaishou.infra.framework.mq.ConsumeResult;
import com.kuaishou.infra.framework.mq.MqConsumer;
import com.kuaishou.infra.framework.mq.MqMessage;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.convert.CommonLogConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.common.service.CommonLogService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.common.LogMessage;
import com.kuaishou.merchant.utils.ProtobufUtil;

import kuaishou.common.BizDef;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-27
 */
@Slf4j
@Lazy
@Service
public class KwaishopQaRiskLogMqConsumer implements MqConsumer {

    @Autowired
    private CommonLogService logService;

    @Autowired
    private CommonLogConvert logConvert;

    private static final String MQ_APP_KEY = "96971b4cf94948f0b56a402d5db99195";

    @Nonnull
    @Override
    public String appKey() {
        return MQ_APP_KEY;
    }

    @Override
    public ConsumeResult onMessage(MqMessage message, ConsumeContext context) {
        LogMessage logMessage = LogMessage.newBuilder().build();
        log.info("[KwaishopQaRiskLogMqConsumer] onMessage message: {}", toJSON(message));
        try {
            logMessage = LogMessage.parseFrom(message.getData());
            log.info("[KwaishopQaRiskLogMqConsumer] onMessage logMessage: {}", ProtobufUtil.protoToJsonString(logMessage));
            logService.createLogRecord(logConvert.buildCreateBO(logMessage));
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            log.error("[KwaishopQaRiskLogMqConsumer] onMessage error, exception: ", e);
            return ConsumeResult.SUCCESS;
        }
    }

    @Nonnull
    @Override
    public String consumerGroup() {
        return c_kwaishop_qa_risk_log_mq.name();
    }

    @Nonnull
    @Override
    public String getLogicTopic() {
        return kwaishop_qa_risk_log_mq.name();
    }

    @Nonnull
    @Override
    public BizDef bizDef() {
        return BizDef.KWAISHOP_QA_RISK;
    }
}
