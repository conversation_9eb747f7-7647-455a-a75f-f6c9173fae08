package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.QUERY_SCENARIO_ERROR;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressInterfaceDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressScenarioDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressScenarioInterfaceRelDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressServiceDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioInterfaceRelDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressInterfaceBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressScenarioBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressServiceBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressScenarioService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class StressScenarioServiceImpl implements StressScenarioService {

    @Resource
    private StressScenarioDAO stressScenarioDAO;

    @Resource
    private StressServiceDAO stressServiceDAO;

    @Resource
    private StressScenarioInterfaceRelDAO relDAO;

    @Resource
    private StressInterfaceDAO stressInterfaceDAO;

    @Override
    public PageBO<StressScenarioDO> queryPageList(QueryStressScenarioBO queryBO) {
        if (queryBO == null) {
            throw new BizException(QUERY_SCENARIO_ERROR);
        }
        return stressScenarioDAO.queryPageList(queryBO);
    }

    @Override
    public PageBO<StressScenarioDO> queryPageListWithInterface(QueryStressScenarioBO queryBO) {
        if (queryBO == null) {
            throw new BizException(QUERY_SCENARIO_ERROR);
        }
        PageBO<StressScenarioDO> pageBO = stressScenarioDAO.queryPageList(queryBO);
        List<Long> scenarioList = pageBO.getData().stream().map(StressScenarioDO::getId).collect(Collectors.toList());
        List<StressScenarioInterfaceRelDO> relList = relDAO.queryStressInterfaceRelByScenario(scenarioList);
        List<Long> interfaceList = relList.stream().map(StressScenarioInterfaceRelDO::getInterfaceId).distinct()
                .collect(Collectors.toList());
        List<StressInterfaceDO> stressInterfaces = stressInterfaceDAO.queryStressInterfaceListByScenario(interfaceList);
        List<StressScenarioDO> scenarioDOList = pageBO.getData();
        for (int i = 0; i < scenarioDOList.size(); i++) {
            StressScenarioDO scenarioDO = scenarioDOList.get(i);
            Long id = scenarioDO.getId();
            List siList = new ArrayList();
            for (int j = 0; j < relList.size(); j++) {
                if (relList.get(j).getScenarioId() == id) {
                    final Long relInterfaceId = relList.get(j).getInterfaceId();
                    siList.addAll(stressInterfaces.stream().filter(p -> relInterfaceId == p.getId())
                            .collect(Collectors.toList()));
                }
            }
            scenarioDO.setStressInterfaces(siList);

        }

        return pageBO;
    }

    @Override
    public Boolean createScenarioFromImport(String importStr) {
        //        JSONObject json = JSONObject.parseObject(importStr);
        Map<String, Object> map = (Map<String, Object>) ObjectMapperUtils.fromJson(importStr).getOrDefault("data",
                new HashMap<>());
        String scenarioName = (String) map.getOrDefault("name", "");
        if (scenarioName.trim().length() == 0) {
            return false;
        }
        StressScenarioBO stressScenarioBO = StressScenarioBO.builder()
                .outId(map.getOrDefault("id", "").toString())
                .name(map.getOrDefault("name", "").toString())
                .status(1)
                .ownerId(0L)
                .outUrl("https://ptp.corp.kuaishou.com/pressure/cases/detail?biz=plateco&caseId=" + map.getOrDefault(
                        "id", "").toString())
                .deleted(0)
                .build();
        String serviceGroup = map.getOrDefault("serviceGroup", "").toString();
        StressScenarioDO stressScenarioDO =
                (StressScenarioDO) stressScenarioDAO.createStressScenarioOrUpdate(stressScenarioBO);
        Map<String, Object> apisUrlToIdMap = (Map<String, Object>) map.getOrDefault("apisUrlToIdMap", new HashMap());
        apisUrlToIdMap.entrySet().forEach((Map.Entry entry) -> {
            String serviceName = entry.getKey().toString();
            StressServiceBO stressServiceBO =
                    StressServiceBO.builder().serviceName(serviceName).serviceDesc("").serviceGroup(serviceGroup)
                            .status(1).deleted(0).build();
            StressServiceDO stressServiceDO = stressServiceDAO.createStressServiceOrUpdate(stressServiceBO);
            if (entry.getValue() != null) {
                Map<String, Object> interfaceMap = (Map<String, Object>) entry.getValue();
                interfaceMap.entrySet().forEach((Map.Entry iEntry) -> {
                    StressInterfaceBO interfaceBO =
                            StressInterfaceBO.builder().interfaceName(iEntry.getKey().toString()).interfaceDesc("")
                                    .serviceId(stressServiceDO.getId()).serviceName(stressServiceDO.getServiceName())
                                    .status(1).deleted(0).updateTime(System.currentTimeMillis()).build();
                    StressInterfaceDO interfaceDO = stressInterfaceDAO.createStressInterfaceOrUpdate(interfaceBO);
                    StressScenarioInterfaceRelDO relDO =
                            StressScenarioInterfaceRelDO.builder().scenarioId(stressScenarioDO.getId())
                                    .interfaceId(interfaceDO.getId()).scenarioName(stressScenarioDO.getName())
                                    .interfaceName(interfaceDO.getInterfaceName()).serviceId(stressServiceDO.getId())
                                    .serviceName(stressServiceDO.getServiceName()).status(1).deleted(0)
                                    .createTime(System.currentTimeMillis()).updateTime(System.currentTimeMillis())
                                    .build();
                    relDAO.createRelOrUpdate(relDO);
                });
            }
        });
        return Boolean.TRUE;
    }


    @Override
    public StressScenarioDO getScenario(StressScenarioBO scenarioBO) {
        return null;
    }


}
