package com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException.throwBizException;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_NOT_FOUND_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.MEMBER_NAME_REPEAT_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.MEMBER_NOT_FOUND_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.MEMBER_STATUS_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.MEMBER_TYPE_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.OPERATOR_AUTH_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.TEAM_ID_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.TEAM_TYPE_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskStringListConfigKey.adminUserNameList;
import static com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.MemberApproveStatusEnum.SUCCESS;
import static com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.MemberApproveStatusEnum.WAIT;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.dao.entity.EntityDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertFactory;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityDTO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityAbstract;
import com.kuaishou.kwaishop.qa.risk.center.task.kbus.model.CommonDataChangeEvent;
import com.kuaishou.kwaishop.qa.risk.center.utils.kim.KimUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@Slf4j
@Lazy
@Service
public class MemberEntityServiceImpl extends EntityAbstract {

    private static final String NAME = "approve";

    @Autowired
    private EntityDAO entityDAO;

    @Autowired
    private EntityConvertFactory entityConvertFactory;

    @Override
    public EntityDTO createEntity(EntityBO entityBO) {
        teamCheck(entityBO);
        // 是否重名
        EntityDO repeatDO = entityDAO.queryByName(getEntityType(), entityBO.getEntityId(), entityBO.getName());
        if (repeatDO != null) {
            throwBizException(MEMBER_NAME_REPEAT_ERROR);
        }
        // 主动加入的需要审批
        entityBO.setStatus(WAIT.getCode());
        EntityConvertHandler handler = entityConvertFactory.getHandler(getEntityType());
        EntityDO createDO = handler.buildCreateDO(entityBO);
        createDO = insert(createDO);
        return handler.buildDOToDTO(createDO);
    }

    @Override
    public void modifyEntity(EntityBO entityBO) {
        if (!adminUserNameList.get().contains(entityBO.getOperator())) {
            throwBizException(OPERATOR_AUTH_ERROR);
        }
        // 成员是否存在
        EntityDO existDO = entityDAO.queryById(entityBO.getId());
        if (existDO == null) {
            throwBizException(MEMBER_NOT_FOUND_ERROR);
        }
        // 判断是否是成员类型
        if (!existDO.getEntityType().equals(getEntityType())) {
            throwBizException(MEMBER_TYPE_ERROR);
        }
        if (!existDO.getStatus().equals(WAIT.getCode())) {
            log.error("[MemberEntityServiceImpl] modifyEntity error, member status error, {}", toJSON(existDO));
            throwBizException(MEMBER_STATUS_ERROR);
        }
        entityBO.setStatus(SUCCESS.getCode());
        EntityConvertHandler handler = entityConvertFactory.getHandler(getEntityType());
        EntityDO modifyDO = handler.buildModifyDO(existDO, entityBO);
        update(modifyDO);
    }

    @Override
    public Integer getEntityType() {
        return EntityTypeEnum.MEMBER_TYPE.getCode();
    }

    @Override
    protected void deleteCheck(EntityDO existDO, String operator) {

    }

    @Override
    public void handlerInsertEvent(CommonDataChangeEvent<EntityDO> changeEvent) {
        EntityDO afterData = changeEvent.getAfterData();
        if (afterData.getStatus().equals(WAIT.getCode())) {
            KimUtils.sendMessageByKimMapConfig(NAME, adminUserNameList.get());
        }
    }

    @Override
    public void handlerUpdateEvent(CommonDataChangeEvent<EntityDO> changeEvent) {

    }

    private void teamCheck(EntityBO entityBO) {
        if (entityBO.getEntityId() == null || entityBO.getEntityId() <= 0) {
            throwBizException(TEAM_ID_EMPTY_ERROR);
        }
        // 团队是否存在
        EntityDO teamDO = entityDAO.queryById(entityBO.getEntityId());
        if (teamDO == null) {
            throwBizException(ENTITY_NOT_FOUND_ERROR);
        }
        // 是否为团队类型
        if (!teamDO.getEntityType().equals(EntityTypeEnum.TEAM_TYPE.getCode())) {
            throwBizException(TEAM_TYPE_ERROR);
        }
    }
}
