package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_DURATION_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_RENT_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ACCOUNT_NOT_TEST_TYPE;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ID_NOT_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.PASSWORD_EMPTY_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.RENT_BORROWER_ERROR;
import static com.kuaishou.kwaishop.qa.risk.center.config.kconf.QaRiskJsonMapConfigKey.accountBPMConfig;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.infra.passport.internal.sdk.common.PassportResponse;
import com.kuaishou.infra.passport.internal.sdk.service.PassportUserService;
import com.kuaishou.krpc.config.annotation.KrpcReference;
import com.kuaishou.kwaishop.biz.account.protobuf.AddTagsToSubjectRequest;
import com.kuaishou.kwaishop.biz.account.protobuf.AddTestAccountRequest;
import com.kuaishou.kwaishop.biz.account.protobuf.AddTestAccountResponse;
import com.kuaishou.kwaishop.biz.account.protobuf.KrpcKwaishopBizAccountTagManageServiceGrpc;
import com.kuaishou.kwaishop.biz.account.protobuf.KrpcKwaishopBizAccountTagQueryServiceGrpc;
import com.kuaishou.kwaishop.biz.account.protobuf.KrpcKwaishopTestAccountManageServiceGrpc;
import com.kuaishou.kwaishop.biz.account.protobuf.RemoveTestAccountRequest;
import com.kuaishou.kwaishop.biz.account.protobuf.RemoveTestAccountResponse;
import com.kuaishou.kwaishop.biz.account.protobuf.SaveTagMetaInfoRequest;
import com.kuaishou.kwaishop.biz.account.protobuf.SaveTagMetaInfoResponse;
import com.kuaishou.kwaishop.biz.account.protobuf.TagMetaInfoDTO;
import com.kuaishou.kwaishop.biz.account.protobuf.rd.KrpcKwaishopBizAccountCenterRdServiceGrpc;
import com.kuaishou.kwaishop.biz.account.protobuf.rd.QueryBidByUserIdRequest;
import com.kuaishou.kwaishop.biz.account.protobuf.rd.QueryBidByUserIdResponse;
import com.kuaishou.kwaishop.framework.resultcode.BaseResultCode;
import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.RentTestAccountRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.TagMetaDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.TagTestAccountRelDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.account.UserAccountDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.RentTestAccountRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagMetaDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TagTestAccountRelDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.TestAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.account.UserAccountDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.AccountTagManageBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.RentAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.UserAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.RentAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.TagMetaConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.TestAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.convert.UserAccountConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.RentAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.TestAccountRentBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo.UserAccountBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.RentalStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums.UserStatusEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.RentAccountService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.TestAccountService;
import com.kuaishou.kwaishop.qa.risk.center.domain.account.service.UserAccountService;
import com.kuaishou.kwaishop.qa.risk.center.domain.openapi.service.EmployeeRoleService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ImportUserAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.RentAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ReturnAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.AddTagMetaInfoRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.AddTagsToTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.CreateTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.DisableAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.DisableTagRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.GetAccountTagsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.GetTagMetaInfosRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.PageTestAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.PageTestUserRentAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyRentTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryMyTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.QueryTestAccountsRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.RemoveTagsToSubjectRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.RentTestAccountDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.RentTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.ReturnTestAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.TagBaseInfoDTO;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.accountv2.TestAccountDTO;


import lombok.extern.slf4j.Slf4j;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/7 17:32
 * @注释
 */
@Lazy
@Service
@Slf4j
public class AccountTagManageBizServiceImpl implements AccountTagManageBizService {

    @Autowired
    private TagMetaDAO tagMetaDAO;

    @Autowired
    private TagMetaConvert tagMetaConvert;

    @Autowired
    private UserAccountConvert userAccountConvert;

    @Autowired
    private UserAccountService userAccountService;

    @Autowired
    private RentAccountConvert rentAccountConvert;

    @Autowired
    private RentAccountService rentAccountService;

    @Autowired
    private RentAccountBizService rentAccountBizService;

    @Autowired
    private TestAccountConvert testAccountConvert;

    @Autowired
    private TestAccountService testAccountService;

    @Autowired
    private UserAuthBizService userAuthBizService;

    @Autowired
    private TagTestAccountRelDAO tagTestAccountRelDAO;

    @Autowired
    private RentTestAccountRecordDAO rentTestAccountRecordDAO;

    @Autowired
    private EmployeeRoleService employeeRoleService;

    @Autowired
    private UserAccountBizService userAccountBizService;

    @Autowired
    private PassportUserService passportUserService;

    @Autowired
    private UserAccountDAO userAccountDAO;

    private static final int DURATION = 2160;

    //失败重试次数
    private static final int NUM = 5;

    @KrpcReference(serviceName = "kwaishop-biz-account-center")
    private KrpcKwaishopBizAccountTagManageServiceGrpc.IKwaishopBizAccountTagManageService tagManageService;

    @KrpcReference(serviceName = "kwaishop-biz-account-center")
    private KrpcKwaishopTestAccountManageServiceGrpc.IKwaishopTestAccountManageService testAccountManageService;

    @KrpcReference(serviceName = "kwaishop-biz-account-center")
    private KrpcKwaishopBizAccountTagQueryServiceGrpc.IKwaishopBizAccountTagQueryService tagQueryService;
    @KrpcReference(serviceName = "kwaishop-biz-account-center")
    private KrpcKwaishopBizAccountCenterRdServiceGrpc.IKwaishopBizAccountCenterRdService accountCenterRdService;


    @Override
    public List<TagBaseInfoDTO> getTagMetaInfos(GetTagMetaInfosRequest request) {
        List<String> codesList = request.getCodesList();
        List<TagMetaDO> tagMetaDAOs = new ArrayList<>();
        if (codesList.isEmpty() && request.getTagName().isEmpty()) {
            tagMetaDAOs = tagMetaDAO.selectAll();
        } else if (!codesList.isEmpty()) {
            for (String code : codesList) {
                TagMetaDO tagMetaDO = tagMetaDAO.queryTagMetaByCode(code);
                if (tagMetaDO != null) {
                    tagMetaDAOs.add(tagMetaDO);
                }
            }
        } else {
            TagMetaDO tagMetaDO = tagMetaDAO.queryTagMetaByName(request.getTagName());
            if (tagMetaDO != null) {
                tagMetaDAOs.add(tagMetaDO);
            }
        }
        return tagMetaConvert.buildTagListInfoDTO(tagMetaDAOs);
    }

    @Override
    public void addTagMetaInfo(AddTagMetaInfoRequest request) {
        List<TagBaseInfoDTO> tagInfoList = request.getTagsList();
        if (tagInfoList.isEmpty()) {
            return;
        }
        List<TagMetaDO> tagMetaDOS = tagMetaConvert.buildTagInfoDOs(tagInfoList);
        for (TagMetaDO tagMetaDO : tagMetaDOS) {
            TagMetaInfoDTO tagMetaInfoDTO = TagMetaInfoDTO.newBuilder()
                    .setTagCode(tagMetaDO.getTagValue())
                    .setTagName(tagMetaDO.getTagName())
                    .setCreateTime(tagMetaDO.getCreateTime())
                    .setUpdateTime(tagMetaDO.getUpdateTime())
                    .build();
            SaveTagMetaInfoRequest tagMetaInfoRequest = SaveTagMetaInfoRequest.newBuilder()
                    .setTagMetaInfo(tagMetaInfoDTO)
                    .build();
            SaveTagMetaInfoResponse saveTagMetaInfoResponse = tagManageService.saveTagMetaInfo(tagMetaInfoRequest);
            log.info("pspStatus:{}", saveTagMetaInfoResponse.getResult().getCode());
            if (saveTagMetaInfoResponse.getResult().getCode() == BaseResultCode.SUCCESS_VALUE) {
                TagMetaDO tagDo = tagMetaDAO.queryTagMetaByCode(tagMetaDO.getTagValue());
                log.info("pspDo:{}", tagDo);
                if (tagDo == null) {
                    long l = tagMetaDAO.insert(tagMetaDO);
                    log.info("psp:{}", l);
                }
            }

        }

    }

    @SuppressWarnings("unchecked")
    @Override
    public void addTagsToTestAccount(AddTagsToTestAccountRequest request) {
        List<String> tagList = request.getTagCodesList();
        if (tagList.isEmpty()) {
            return;
        }
        AddTagsToSubjectRequest addTagsToSubjectRequest = AddTagsToSubjectRequest.newBuilder()
                .setSubjectType("SELLER")
                .setSubjectId(request.getUserId())
                .addAllTagCodes(tagList)
                .build();
        //失败重试
        int resultCode = BaseResultCode.UNKNOWN_VALUE;
        for (int i = 0; i < NUM; i++) {
            resultCode = tagManageService.addTagsToSubject(addTagsToSubjectRequest).getResult().getCode();
            if (resultCode == BaseResultCode.SUCCESS_VALUE) {
                break;
            }
        }

        List<TagTestAccountRelDO> testTagInAccount = tagTestAccountRelDAO
                .getTestTagInAccount(request.getUserId(), Arrays.asList(2));
        List<String> tags = tagList.stream().filter(tag -> testTagInAccount.stream()
                        .noneMatch(account -> account.getTagValue().equals(tag) && account.getDeleted() == 0))
                .collect(Collectors.toList());
        if (tags.isEmpty()) {
            return;
        }
        for (String tag : tags) {
            //筛选出未加入的tag，加入数据库中
            TagTestAccountRelDO tagTestAccountRelDO = testAccountConvert.buildTagTestAccountRelDO(tag, request);
            tagTestAccountRelDAO.insert(tagTestAccountRelDO);
        }

    }

    @Override
    public void removeTagsFromTestAccount(RemoveTagsToSubjectRequest request) {
        List<String> tagList = request.getTagCodesList();
        if (tagList.isEmpty()) {
            return;
        }
        com.kuaishou.kwaishop.biz.account.protobuf.RemoveTagsToSubjectRequest removeTagsToSubjectRequest =
                com.kuaishou.kwaishop.biz.account.protobuf.RemoveTagsToSubjectRequest.newBuilder()
                        .setSubjectId(request.getUserId())
                        .setSubjectType("SELLER")
                        .addAllTagCodes(tagList)
                        .build();

        //失败重试
        int resultCode = BaseResultCode.UNKNOWN_VALUE;
        for (int i = 0; i < NUM; i++) {
            resultCode = tagManageService.removeTagsToSubject(removeTagsToSubjectRequest).getResult().getCode();
            if (resultCode == BaseResultCode.SUCCESS_VALUE) {
                break;
            }
        }

        List<TagTestAccountRelDO> testTagInAccount = tagTestAccountRelDAO
                .getTestTagInAccount(request.getUserId(), Arrays.asList(2));
        //筛选出有效、可删除的tags
        List<String> tags = tagList.stream().filter(tag -> testTagInAccount.stream()
                        .anyMatch(account -> account.getTagValue().equals(tag)))
                .collect(Collectors.toList());
        for (String tag : tags) {
            tagTestAccountRelDAO.deleteTagFromAccount(request.getUserId(), tag);
        }


    }

    @Override
    @SuppressWarnings("unchecked")
    public void createTestAccount(CreateTestAccountRequest request) {
        //前置检查项
        createAccountCheck(request);
        //暂时先填充必要信息，account电商信息待补充
        TestAccountDO testAccountDO = userAccountConvert.buildCreateDO(request);
        //填充bid
        QueryBidByUserIdResponse queryBidByUserIdResponse = accountCenterRdService
                .queryBidByUserId(QueryBidByUserIdRequest.newBuilder()
                        .setUserId(request.getUserId())
                        .build());
        long bid = queryBidByUserIdResponse.getBid();
        testAccountDO.setBId(bid);
        //填充所属团队信息
        //String employeeContext = employeeRoleService.getEmployee(request.getOperator());

        AddTestAccountRequest addTestAccountRequest = AddTestAccountRequest.newBuilder()
                .addAccounts(request.getUserId())
                .setSubjectType("SELLER")
                .setOperator(request.getOperator())
                .build();
        AddTestAccountResponse addTestAccountResponse = testAccountManageService.addTestAccount(addTestAccountRequest);

        userAccountService.createAccount(testAccountDO);
        //直接同步老表
        ImportUserAccountRequest importUserAccountRequest = ImportUserAccountRequest.newBuilder()
                .setOperator(request.getOperator())
                .setTeamId(0)
                .setAccount(request.getAccount())
                .setPassword(request.getPassword())
                .setLoginType(request.getLoginType())
                .setUserId(request.getUserId())
                .setStatus(1)
                .build();
        userAccountBizService.importUserAccount(importUserAccountRequest);


        //添加账号者直接完成当前账号的租借
        RentTestAccountRequest rentTestAccountRequest = RentTestAccountRequest.newBuilder()
                .setUserId(request.getUserId())
                .setBorrower(request.getOperator())
                .setDuration(DURATION)
                .build();
        rentTestAccount(rentTestAccountRequest);

    }

    @Override
    public void rentTestAccount(RentTestAccountRequest request) {
        rentParamsCheck(request);
        List<TestAccountDO> testAccountDOS;
        if (request.getBid() > 0) {
            testAccountDOS = userAccountService.queryAccountByUserIdV2(request.getBid(), true);
        } else {
            testAccountDOS = userAccountService.queryAccountByUserIdV2(request.getUserId(), false);
        }
        if (testAccountDOS.size() == 0) {
            return;
        }
        TestAccountDO testAccountDO = testAccountDOS.get(0);
        UserStatusEnum status = UserStatusEnum.of(testAccountDO.getStatus());
        if (status == null || !status.equals(UserStatusEnum.CAN_APPLY)) {
            throw new BizException(ACCOUNT_NOT_RENT_ERROR);
        }
        RentAccountBO rentAccountBO = rentAccountConvert.buildRentAccountBO(testAccountDO, request.getUserId()
                , request.getBorrower(), request.getDuration(), request.getBid());
        //插入租借数据
        rentAccountService.rentAccountV2(rentAccountBO);
        //更改账户表数据
        userAccountService.updateRentStatus(rentAccountBO);

        //同步到老的账号池中
        RentAccountRequest rentAccountRequest = RentAccountRequest.newBuilder()
                .setBorrower(request.getBorrower())
                .setUserId(request.getUserId())
                .setDuration(request.getDuration())
                .build();
        rentAccountBizService.rentAccount(rentAccountRequest);
    }


    @Override
    public PageTestAccountDTO queryTestAccounts(QueryTestAccountsRequest request) {
        UserAccountBO queryBO = userAccountConvert.buildQueryBOV2(request);
        List<TestAccountDO> resList = userAccountService.queryListV2(queryBO);

        return null;
    }

    @Override
    public PageTestAccountDTO queryMyTestAccounts(QueryMyTestAccountsRequest request) {
        return null;
    }


    @Override
    public PageTestUserRentAccountDTO queryMyRentTestAccounts(QueryMyRentTestAccountsRequest request) {
        TestAccountBO queryBO = testAccountConvert.buildQueryBO(request);
        PageBO<RentTestAccountRecordDO> pageBO = testAccountService.queryMyRentRecords(queryBO);

        return buildPageTestUserRentAccountDTO(pageBO);

    }

    @Override
    public PageTestUserRentAccountDTO queryMyRentTestAccountsWithTags(QueryMyRentTestAccountsRequest request) {
        //1.查租借账号 2.查账号详情 3.查账号tag
        TestAccountBO queryBO = testAccountConvert.buildQueryBO(request);

        PageBO<RentTestAccountRecordDO> resList = testAccountService.queryMyRentRecords(queryBO);

        return buildPageTestUserRentAccountDTO(resList);
    }

    @Override
    public PageTestAccountDTO queryTestAccountsWithTags(QueryTestAccountsRequest request) {
        //1.查账号 2.查账号关联tag
        TestAccountBO queryBO = testAccountConvert.buildQueryBO(request);
        PageBO<TestAccountDO> resList = testAccountService.queryAllTestAccountRecords(queryBO);
        //BO To DTO
        return buildPagePageTestUserAccountDTO(resList);
    }

    @Override
    public PageTestAccountDTO queryMyTestAccountsWithTags(QueryMyTestAccountsRequest request) {
        TestAccountBO queryBO = testAccountConvert.buildQueryBO(request);
        PageBO<TestAccountDO> resList = testAccountService.queryMyTestAccountRecords(queryBO);
        return buildPagePageTestUserAccountDTO(resList);
    }

    @Override
    public void returnTestAccount() {

    }

    @Override
    public void returnTestAccount(ReturnTestAccountRequest request) {
        List<RentTestAccountRecordDO> rentTestAccountRecordDOS = testAccountService.queryAllRentRecords(TestAccountRentBO.builder()
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .userId(request.getUserId())
                .borrower(request.getBorrower())
                .build());
        if (rentTestAccountRecordDOS == null || rentTestAccountRecordDOS.isEmpty()) {
            return;
        }
        rentTestAccountRecordDOS.forEach(rentTestAccountRecordDO -> {
                    rentTestAccountRecordDAO.returnAccount(rentTestAccountRecordDO);
                }
        );
        //如果开关打开，同步到老的账号池中
        Map<String, Object> map = accountBPMConfig.getMap(String.class, Object.class);
        if ((boolean) map.get("oldSyncAccountSwitch")) {
            ReturnAccountRequest returnAccountRequest = ReturnAccountRequest.newBuilder()
                    .setBorrower(request.getBorrower())
                    .setUserId(request.getUserId())
                    .build();
            rentAccountBizService.returnAccount(returnAccountRequest);
        }

    }

    @Override
    public void autoReturnAccount() {
        // 全表扫描
        List<RentTestAccountRecordDO> rentTestAccountDOS = testAccountService.queryAllRentRecords(TestAccountRentBO.builder()
                .rentalStatus(RentalStatusEnum.BORROWED.getCode())
                .build());
        if (rentTestAccountDOS.isEmpty()) {
            log.info("没有租借的");
            return;
        }
        long currentTimeMillis = System.currentTimeMillis();
        rentTestAccountDOS.forEach(rentTestAccountDO -> {
            if (rentTestAccountDO.getDuration() < currentTimeMillis) {
                returnTestAccount(ReturnTestAccountRequest.newBuilder()
                        .setBorrower("system")
                        .setUserId(rentTestAccountDO.getTestAccountId())
                        .build()
                );
            }
        });
    }

    @Override
    public void deleteTag(DisableTagRequest request) {
        if (!request.getTagValue().isEmpty()) {
            TagMetaDO tagMetaDO = new TagMetaDO();
            tagMetaDO.setTagValue(request.getTagValue());
            tagMetaDO.setUpdateTime(System.currentTimeMillis());
            tagMetaDO.setModifier(!request.getOperator().isEmpty() ? request.getOperator() : "system");
            tagMetaDAO.deleteById(tagMetaDO);
        }
    }

    @Override
    public TestAccountDTO queryTestAccount(QueryTestAccountRequest request) {
        TestAccountBO queryBO = testAccountConvert.buildQueryBO(request);
        PageBO<TestAccountDO> resList = testAccountService.queryTestAccountPage(queryBO);
        return null;
    }

    @Override
    public void disableAccount(DisableAccountRequest request) {
        TestAccountDO testAccountDO = TestAccountDO.builder()
                .kwaiId(request.getUserId())
                .build();

        RemoveTestAccountRequest removeTestAccountRequest = RemoveTestAccountRequest.newBuilder()
                .addAccounts(request.getUserId())
                .setSubjectType("SELLER")
                .setOperator(request.getOperator())
                .build();
        RemoveTestAccountResponse removeTestAccountResponse = testAccountManageService.removeTestAccount(removeTestAccountRequest);
        if (removeTestAccountResponse.getResult().getCode() == BaseResultCode.SUCCESS_VALUE) {
            userAccountService.disableAccount(testAccountDO);
        }

    }

    @Override
    public List<TagBaseInfoDTO> getAccountTags(GetAccountTagsRequest request) {
        return null;
    }


    private PageTestUserRentAccountDTO buildPageTestUserRentAccountDTO(PageBO<RentTestAccountRecordDO> pageBO) {
        return PageTestUserRentAccountDTO.newBuilder().setPageNo(pageBO.getPageNo()).setPageSize(pageBO.getPageSize())
                .addAllDetails(
                        pageBO.getData().stream().map(item -> convertRentTestAccountDTO(item))
                                .collect(Collectors.toList())
                ).build();
    }


    private PageTestAccountDTO buildPagePageTestUserAccountDTO(PageBO<TestAccountDO> pageBO) {
        return PageTestAccountDTO.newBuilder()
                .setPageNo(pageBO.getPageNo())
                .setPageSize(pageBO.getPageSize())
                .setTotal(pageBO.getTotal())
                .addAllDetails(
                        pageBO.getData().stream().map(item -> convertTestAccountDO(item)).collect(Collectors.toList())
                ).build();
    }

    private TestAccountDTO convertTestAccountDO(TestAccountDO accountDO) {
        List<TagTestAccountRelDO> testAccountTags = accountDO.getTestAccountTags();
        List<TagBaseInfoDTO> tagBaseInfoDTOS = convertTagBaseInfoDTO(testAccountTags);
        return TestAccountDTO.newBuilder()
                .setUserId(accountDO.getKwaiId())
                .setBid(accountDO.getBId())
                .setLoginType(accountDO.getLoginType())
                .setStatus(accountDO.getStatus())
                .setSellerName(accountDO.getSellerName() != null ? accountDO.getSellerName() : "")
                .setStoreName(accountDO.getStoreName() != null ? accountDO.getStoreName() : "")
                .setStoreType(accountDO.getStoreType() != null ? accountDO.getStoreType() : "")
                .setCelebrityPermission(accountDO.getCelebrityPermission())
                .setShopScore(accountDO.getShopScore() != null ? accountDO.getShopScore() : "")
                .setPromoterScore(accountDO.getPromoterScore() != null ? accountDO.getPromoterScore() : "")
                .setDeposit(accountDO.getDeposit() > 0)
                .addAllTagBaseInfo(tagBaseInfoDTOS)
                .build();
    }

    private List<TagBaseInfoDTO> convertTagBaseInfoDTO(List<TagTestAccountRelDO> testAccountTags) {
        return testAccountTags.stream()
                .map(tag -> TagBaseInfoDTO.newBuilder()
                        .setTagValue(tag.getTagValue())
                        .setTagName(tag.getTagName())
                        .build())
                .collect(Collectors.toList());
    }

    private RentTestAccountDTO convertRentTestAccountDTO(RentTestAccountRecordDO rentTestAccountDO) {
        // 将时间戳转换为 LocalDateTime
        LocalDateTime dateTime = Instant.ofEpochMilli(rentTestAccountDO.getDueTime())
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return RentTestAccountDTO.newBuilder().setId(rentTestAccountDO.getId())
                .setTestUserAccount(convertTestAccountDO(rentTestAccountDO.getTestAccount()))
                .setDueTime(rentTestAccountDO.getDueTime())
                .setDueTimeDesc(dateTime.format(formatter))
                .build();
    }


    public void createAccountCheck(CreateTestAccountRequest request) {
        if (StringUtils.isBlank(request.getAccount())) {
            throw new BizException(ACCOUNT_EMPTY_ERROR);
        }
        if (StringUtils.isBlank(request.getPassword())) {
            throw new BizException(PASSWORD_EMPTY_ERROR);
        }
        if (request.getBid() <= 0 && request.getUserId() <= 0) {
            throw new BizException(ID_NOT_EMPTY_ERROR);
        }
        UserAccountDO existDO = userAccountDAO.queryByUid(request.getUserId());
        PassportResponse<Boolean> ksTestAccount = passportUserService.isKsTestAccount(request.getUserId());
        if ((ksTestAccount.isSuccess() && !ksTestAccount.getResult()) && existDO == null) {
            throw new BizException(ACCOUNT_NOT_TEST_TYPE);
        }
    }

    private void rentParamsCheck(RentTestAccountRequest request) {
        if (request.getDuration() == 0) {
            throw new BizException(ACCOUNT_NOT_DURATION_ERROR);
        }
        if (request.getBorrower().isEmpty()) {
            throw new BizException(RENT_BORROWER_ERROR);
        }
    }

}
