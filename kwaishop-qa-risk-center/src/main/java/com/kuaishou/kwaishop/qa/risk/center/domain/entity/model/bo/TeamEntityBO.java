package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TeamEntityBO extends EntityBO {

    /**
     * 扩展字段
     */
    private String ext;

    /**
     * 负责人
     */
    private String leader;

}
