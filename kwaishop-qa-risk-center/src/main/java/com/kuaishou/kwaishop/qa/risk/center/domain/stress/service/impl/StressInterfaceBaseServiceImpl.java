package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.QUERY_SCENARIO_ERROR;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressInterfaceBaseDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceBaseDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressInterfaceBaseBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressInterfaceBaseService;

@Service
public class StressInterfaceBaseServiceImpl implements StressInterfaceBaseService {

    @Resource
    private StressInterfaceBaseDAO interfaceBaseDAO;

    @Override
    public PageBO<StressInterfaceBaseDO> queryPageList(QueryStressInterfaceBaseBO queryBO) {
        if (queryBO == null) {
            throw new BizException(QUERY_SCENARIO_ERROR);
        }
        return  interfaceBaseDAO.queryStressInterfaceBaseList(queryBO);
    }
}
