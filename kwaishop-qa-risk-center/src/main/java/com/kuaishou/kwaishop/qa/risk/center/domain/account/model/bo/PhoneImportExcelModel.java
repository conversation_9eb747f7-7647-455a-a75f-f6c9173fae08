package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo;


import org.apache.commons.lang3.StringUtils;

import com.alibaba.excel.annotation.ExcelProperty;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.annotation.ExcelNotNull;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.annotation.ExcelStringFormat;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.BaseExcelModel;
import com.kuaishou.kwaishop.qa.risk.center.utils.excel.bo.ValidResultBO;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
@Data
public class PhoneImportExcelModel extends BaseExcelModel {

//    @ExcelProperty("token")
//    @ExcelStringFormat
//    private String token;

    @ExcelNotNull
    @ExcelProperty("userId")
    private Long userId;

    @ExcelNotNull
    @ExcelStringFormat
    @ExcelProperty("account")
    private String account;

    @ExcelNotNull
    @ExcelProperty("loginType")
    private int loginType;

    @ExcelNotNull
    @ExcelStringFormat
    @ExcelProperty("password")
    private String password;

//    @ExcelStringFormat
//    @ExcelProperty("kuaishou.api_st")
//    private String apiSt;
//
//    @ExcelStringFormat
//    @ExcelNotNull
//    @ExcelProperty("mobileCountryCode")
//    private String mobileCountryCode;
//
//    @ExcelStringFormat
//    @ExcelNotNull
//    @ExcelProperty("token_client_salt")
//    private String tokenSalt;
//
//    @ExcelStringFormat
//    @ExcelNotNull
//    @ExcelProperty("kuaishou.api_client_salt")
//    private String apiSalt;

    @Override
    public boolean equals(Object o) {
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public ValidResultBO customValid() {
        ValidResultBO res = new ValidResultBO();
        res.setResult(Boolean.TRUE);
        res.setFailMsg(StringUtils.EMPTY);
//        if (!RegexUtils.isMobile(account)) {
//            res.setResult(Boolean.FALSE);
//            res.setFailMsg(String.format("%s 手机号不正确!", account));
//        }
//        if (!mobileCountryCodeList.get().contains(mobileCountryCode)) {
//            res.setResult(Boolean.FALSE);
//            res.setFailMsg(String.format("%s 手机区号不正确", account));
//        }
        return res;
    }
}
