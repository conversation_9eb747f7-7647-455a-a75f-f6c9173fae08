package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EntityBaseBO extends EntityBO {
    /**
     * 扩展字段
     */
    private String ext;
    /**
     * 扩展字段1
     */
    private String extra1;
    /**
     * 扩展字段2
     */
    private String extra2;
    /**
     * 扩展字段3
     */
    private String extra3;
}
