package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressSerBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressSerService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class StressSerServiceImpl implements StressSerService {

    @Override
    public PageBO<StressServiceDO> queryPageList(QueryStressSerBO queryBO) {
        return null;
    }

    @Override
    public PageBO<StressServiceDO> queryPageListWithScenario(QueryStressSerBO queryBO, List<Long> scenarioIds) {
        return null;
    }

    @Override
    public PageBO<StressServiceDO> queryPageListWithService(QueryStressSerBO queryBO, List<Long> serviceIds) {
        return null;
    }

    @Override
    public StressServiceDO queryInterfaceDetail(Long id) {
        return null;
    }

    @Override
    public Integer createOrUpdateInterface(StressServiceDO serBO) {
        return null;
    }

    @Override
    public Integer batchCreateInterface(List<StressServiceDO> serBOs) {
        return null;
    }
}
