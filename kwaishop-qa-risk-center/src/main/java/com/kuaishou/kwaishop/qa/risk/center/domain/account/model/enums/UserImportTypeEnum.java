package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.enums;

import java.util.ArrayList;
import java.util.List;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.combine.EnumInfo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-29
 */
public enum UserImportTypeEnum {

    PHONE_IMPORT(1, "手机号导入excel"),
    EMAIL_IMPORT(2, "邮箱导入excel"),
    SINGLE_UID_IMPORT(3, "单UID导入excel"),

    TOKEN(4, "token导入")
    ;

    private final Integer code;
    private final String desc;

    UserImportTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static UserImportTypeEnum of(Integer code) {
        for (UserImportTypeEnum typeEnum: values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }

    public static List<EnumInfo> buildEnumInfo() {
        List<EnumInfo> res = new ArrayList<>();
        for (UserImportTypeEnum typeEnum: values()) {
            EnumInfo enumInfo = EnumInfo.newBuilder()
                    .setValue(typeEnum.getCode())
                    .setDesc(typeEnum.getDesc())
                    .build();
            res.add(enumInfo);
        }
        return res;
    }
}
