package com.kuaishou.kwaishop.qa.risk.center.db.mapper.datatool;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.DATA_CASE_TABLE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.datatool.DataCaseDO;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-28
 */

@Mapper
@DataSourceRouting(DATA_CASE_TABLE_NAME)
public interface DataCaseMapper extends BaseMapper<DataCaseDO> {

    @Update("update data_case set modifier = #{operator}, deleted = 1 where id = #{id}")
    int logicDeleted(@Param("operator") String operator, @Param("id") Long id);


    @Select("select * from data_case where  deleted=0 and name like CONCAT('%',#{name},'%') ")
    List<DataCaseDO> queryDataCaseLikeName(@Param("name") String name);

    @Select("select * from data_case  where deleted=0 and id = #{id} ")
    DataCaseDO queryDataCaseById(@Param("id") Long id);

    @Select("select * from data_case  where deleted=0 ")
    List<DataCaseDO> queryDataCase();

    @Select("select * from data_case  where deleted=0 and id in #{ids}")
    List<DataCaseDO> queryDataCaseByIds(@Param("ids") List<Long> ids);

}
