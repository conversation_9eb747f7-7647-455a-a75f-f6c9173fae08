package com.kuaishou.kwaishop.qa.risk.center.domain.entity.service;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.ENTITY_DATA_DATA_TYPE_ERROR;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityDataTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-30
 */
@Slf4j
@Lazy
@Service
public class EntityDataCalFactory {

    @Autowired
    private List<EntityDataCalService> entityDataCalServices;

    private Map<Integer, EntityDataCalService> calServiceMap;

    @PostConstruct
    private void init() {
        calServiceMap = new HashMap<>();
        entityDataCalServices.forEach(p -> calServiceMap.put(p.getCode(), p));
    }

    public EntityDataCalService getService(Integer code) {
        if (EntityDataTypeEnum.of(code) != null) {
            if (calServiceMap.containsKey(code)) {
                return calServiceMap.get(code);
            }
        }
        throw new BizException(ENTITY_DATA_DATA_TYPE_ERROR);
    }
}
