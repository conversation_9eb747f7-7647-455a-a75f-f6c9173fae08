package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo;

import java.util.Collection;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TestAccountRentBO {
    private Long id;

    private Long userId;

    private Long teamId;

    private Long centerId;

    private String borrower;

    /**
     * 登录类型，1- 86手机号登录，2- 1264手机号登录，3- 邮箱登录
     */
    private Integer loginType;

    private Integer duration;

    private Integer rentalStatus;

    private Long rentalTime;

    private Long dueTime;

    private Collection<Long> userIds;

    private Collection<Long> ids;

    private Collection<Integer> loginTypes;

    private Integer pageNo;

    private Integer pageSize;

    private String password;

    private String account;

    private String modifier;

    private Long updateTime;
}
