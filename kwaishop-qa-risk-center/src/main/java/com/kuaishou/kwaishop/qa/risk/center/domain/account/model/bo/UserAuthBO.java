package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo;

import java.math.BigDecimal;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class UserAuthBO {

    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 店铺名
     */
    private String storeName;

    /**
     * 店铺类型
     */
    private String storeType;

    private Boolean isSeller; //是否卖家

    private BigDecimal deposit; //保证

    private Integer merchantPermission; //是否开通商家权限

    private Integer distributorPermission; //商家分销权限

    private Integer celebrityPermission; //达人分销权限

    private Integer recruitingLeader; //招商团长权限

    private String comment; //评论

    private Long updateTime;

    private String modifier;

    private Long createTime;

    private Integer pageNo;

    private Integer pageSize;

    private List<Long> userIds;
}
