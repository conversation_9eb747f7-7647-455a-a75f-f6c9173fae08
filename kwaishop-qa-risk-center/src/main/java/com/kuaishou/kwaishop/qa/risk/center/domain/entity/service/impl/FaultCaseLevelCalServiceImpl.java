package com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.impl;

import static com.kuaishou.framework.util.ObjectMapperUtils.fromJSON;
import static com.kuaishou.framework.util.ObjectMapperUtils.toJSON;
import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;
import static com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum.TEAM_TYPE;
import static com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCombineDateEnum.LAST_30_DAY;
import static com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCombineDateEnum.LAST_7_DAY;
import static com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCombineDateEnum.LAST_DAY;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.kuaishou.infra.boot.jdbc.datasource.TransactionalDataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.fault.FaultPlanRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultCaseDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.fault.FaultPlanRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityDataConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityDataTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityDataCalService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityDataService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityService;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.service.EntityServiceFactory;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.convert.FaultCaseConvert;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCaseCombineBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCaseExecInfoBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.bo.FaultCaseExtBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCaseLevelTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.model.enums.FaultCombineDateEnum;
import com.kuaishou.kwaishop.qa.risk.center.domain.fault.service.FaultCaseService;
import com.kuaishou.kwaishop.qa.risk.center.utils.date.LocalDateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-30
 */
@Service
@Lazy
@Slf4j
public class FaultCaseLevelCalServiceImpl implements EntityDataCalService {

    @Autowired
    private EntityServiceFactory serviceFactory;

    @Autowired
    private FaultCaseConvert faultCaseConvert;

    @Autowired
    private FaultCaseService faultCaseService;

    @Autowired
    private FaultPlanRecordDAO faultPlanRecordDAO;

    @Autowired
    private EntityDataConvert entityDataConvert;

    @Autowired
    private EntityDataService entityDataService;

    @TransactionalDataSourceRouting(RISK_CENTER_SOURCE_NAME)
    @Override
    public void exec(String dt) {
        EntityService teamService = serviceFactory.getEntityService(TEAM_TYPE.getCode());
        List<EntityDO> teamDOList = teamService.queryByEntityType();
        long dtEnd = LocalDateUtil.stringBasicToDayEndMilli(dt);
        teamDOList.forEach(e -> {
            List<FaultCaseDO> teamCaseDOList = faultCaseService.queryCaseList(
                    faultCaseConvert.buildTeamQueryBO(e.getEntityId(), e.getId()));
            Map<FaultCaseLevelTypeEnum, Long> levelMap = getLevelDefaultMap();
            Map<FaultCaseLevelTypeEnum, Map<FaultCombineDateEnum, Long>> levelDateMap = getLevelDateCoverDefaultMap();
            Map<FaultCaseLevelTypeEnum, Map<FaultCombineDateEnum, Set<Long>>> coverPlanMap = getLevelCoverPlanDefaultMap();
            teamCaseDOList.forEach(p -> {
                FaultCaseLevelTypeEnum caseLevel = FaultCaseLevelTypeEnum.of(p.getCalleeLevel());
                if (caseLevel == null) {
                    return;
                }
                levelMap.merge(caseLevel, 1L, Long::sum);
                FaultCaseExtBO extBO = fromJSON(p.getExt(), FaultCaseExtBO.class);
                Map<FaultCombineDateEnum, Long> dateMap = levelDateMap.get(caseLevel);
                Map<FaultCombineDateEnum, Set<Long>> planMap = coverPlanMap.get(caseLevel);
                if (extBO != null && extBO.getFaultCaseExecInfoBO() != null) {
                    FaultCaseExecInfoBO execInfoBO = extBO.getFaultCaseExecInfoBO();
                    if (execInfoBO.getFaultPlanRecordId() == null) {
                        return;
                    }
                    Map<FaultCombineDateEnum, Long> caseDateMap;
                    Long planId;
                    if (execInfoBO.getFaultPlanRecordTime() != null && execInfoBO.getFaultPlanId() != null) {
                        caseDateMap = getDate(execInfoBO.getFaultPlanRecordTime(), dtEnd);
                        planId = execInfoBO.getFaultPlanId();
                    } else {
                        FaultPlanRecordDO recordDO = faultPlanRecordDAO.queryById(execInfoBO.getFaultPlanRecordId());
                        caseDateMap = getDate(recordDO.getUpdateTime(), dtEnd);
                        planId = recordDO.getPlanId();
                    }
                    caseDateMap.forEach((k, v) -> {
                        dateMap.merge(k, v, Long::sum);
                        if (v > 0) {
                            planMap.get(k).add(planId);
                        }
                    });
                }
            });
            FaultCaseCombineBO combineBO = faultCaseConvert.buildCaseCombineBO(levelMap, levelDateMap, coverPlanMap);
            entityDataService.createEntityData(entityDataConvert.buildCreateBO(e.getId(), TEAM_TYPE.getCode(), getCode(), dt, toJSON(combineBO)));
        });
    }

    private Map<FaultCaseLevelTypeEnum, Long> getLevelDefaultMap() {
        Map<FaultCaseLevelTypeEnum, Long> res = Maps.newHashMap();
        for (FaultCaseLevelTypeEnum typeEnum: FaultCaseLevelTypeEnum.values()) {
            res.put(typeEnum, 0L);
        }
        return res;
    }

    private Map<FaultCaseLevelTypeEnum, Map<FaultCombineDateEnum, Long>> getLevelDateCoverDefaultMap() {
        Map<FaultCaseLevelTypeEnum, Map<FaultCombineDateEnum, Long>> res = Maps.newHashMap();
        for (FaultCaseLevelTypeEnum typeEnum: FaultCaseLevelTypeEnum.values()) {
            Map<FaultCombineDateEnum, Long> dtMap = Maps.newHashMap();
            for (FaultCombineDateEnum dateEnum: FaultCombineDateEnum.values()) {
                dtMap.put(dateEnum, 0L);
            }
            res.put(typeEnum, dtMap);
        }
        return res;
    }

    private Map<FaultCaseLevelTypeEnum, Map<FaultCombineDateEnum, Set<Long>>> getLevelCoverPlanDefaultMap() {
        Map<FaultCaseLevelTypeEnum, Map<FaultCombineDateEnum, Set<Long>>> res = Maps.newHashMap();
        for (FaultCaseLevelTypeEnum typeEnum: FaultCaseLevelTypeEnum.values()) {
            Map<FaultCombineDateEnum, Set<Long>> dtMap = Maps.newHashMap();
            for (FaultCombineDateEnum dateEnum: FaultCombineDateEnum.values()) {
                dtMap.put(dateEnum, new HashSet<>());
            }
            res.put(typeEnum, dtMap);
        }
        return res;
    }

    private Map<FaultCombineDateEnum, Long> getDate(Long currentTime, Long sourceTime) {
        Map<FaultCombineDateEnum, Long> res = Maps.newHashMap();
        if (LocalDateUtil.dateCompareAfter(currentTime, sourceTime, 30)) {
            res.put(LAST_30_DAY, 1L);
        }
        if (LocalDateUtil.dateCompareAfter(currentTime, sourceTime, 7)) {
            res.put(LAST_7_DAY, 1L);
        }
        if (LocalDateUtil.dateCompareAfter(currentTime, sourceTime, 1)) {
            res.put(LAST_DAY, 1L);
        }
        return res;
    }


    @Override
    public Integer getCode() {
        return EntityDataTypeEnum.FAULT_CASE_LEVEL_DATA.getCode();
    }
}
