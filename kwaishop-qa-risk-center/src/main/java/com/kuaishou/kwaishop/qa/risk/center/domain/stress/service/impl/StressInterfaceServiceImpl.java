package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.StressInterfaceBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressInterfaceService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class StressInterfaceServiceImpl implements StressInterfaceService {


    @Override
    public PageBO<StressInterfaceDO> queryPageList(QueryStressScenarioBO queryBO) {
        return null;
    }

    @Override
    public PageBO<StressInterfaceDO> queryPageListWithScenario(QueryStressScenarioBO queryBO, List<Long> scenarioIds) {
        return null;
    }

    @Override
    public PageBO<StressInterfaceDO> queryPageListWithService(QueryStressScenarioBO queryBO, List<Long> serviceIds) {
        return null;
    }

    @Override
    public StressInterfaceDO queryInterfaceDetail(Long id) {
        return null;
    }

    @Override
    public Integer createOrUpdateInterface(StressInterfaceBO interfaceBO) {
        return null;
    }

    @Override
    public Integer batchCreateInterface(List<StressInterfaceBO> interfaceBOs) {
        return null;
    }
}
