package com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressInterfaceRecordBO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StressInterfaceRecordMapper extends BaseMapper<StressInterfaceRecordDO> {
    //后续加上场景，可能每个场景的接口基线不一致 当前一个接口只有一个基线 TODO
    @Select({"<script>"
            + "SELECT * FROM stress_interface_record "
            + " WHERE "
            + "id = #{id}"
            + "</script>"})
    StressInterfaceRecordDO queryStressInterfaceRecordById(@Param("id") Long id);


    @Select({"<script>"
            + "SELECT * FROM stress_interface_record "
            + " WHERE 1=1"
            + "<if test='scenarioRecordId > 0'> and scenario_record_id = #{scenarioRecordId}</if>"
            + "</script>"})
    List<StressInterfaceRecordDO> queryStressInterfaceRecords(QueryStressInterfaceRecordBO queryBO);
}
