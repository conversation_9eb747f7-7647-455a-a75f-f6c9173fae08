package com.kuaishou.kwaishop.qa.risk.center.domain.account.biz;

import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.BatchCheckAccountRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.CheckAccountRequest;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/10/16 15:29
 * @注释
 */
public interface CheckAccountBizService {

    boolean checkAccount(CheckAccountRequest request);

    boolean batchCheckAccount(BatchCheckAccountRequest batchCheckAccountRequest);

    void checkAndDeleteAccount();



}
