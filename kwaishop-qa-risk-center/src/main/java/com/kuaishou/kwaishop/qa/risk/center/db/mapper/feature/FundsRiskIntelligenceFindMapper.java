package com.kuaishou.kwaishop.qa.risk.center.db.mapper.feature;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.feature.FundRiskIntelligenceFindDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface FundsRiskIntelligenceFindMapper extends BaseMapper<FundRiskIntelligenceFindDO> {
    @Select(" select * from funds_risk_fields "
            + "where id = #{id}")
    FundRiskIntelligenceFindDO queryRiskFieldsByMainId(@Param("id") String id);

    @Update("update funds_risk_fields set updater = #{updater}, update_time = #{updateTime}, risk_level = #{riskLevel} "
            + "where id = #{id}")
    int updateRiskLevelByMainId(@Param("updater") String updater, @Param("updateTime") Long updateTime,
                               @Param("riskLevel") String riskLevel, @Param("id") Long id);
}
