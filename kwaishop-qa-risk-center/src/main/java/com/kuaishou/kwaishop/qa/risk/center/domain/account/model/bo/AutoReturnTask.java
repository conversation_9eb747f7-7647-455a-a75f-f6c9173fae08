package com.kuaishou.kwaishop.qa.risk.center.domain.account.model.bo;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.kuaishou.kwaishop.qa.risk.center.domain.account.biz.RentAccountBizService;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.account.ReturnAccountRequest;

import lombok.AllArgsConstructor;
import lombok.Builder;

@AllArgsConstructor
@Builder
public class AutoReturnTask implements Delayed {

    @Resource
    private RentAccountBizService rentAccountBizService;

    // 任务的唯一 ID
    private String taskId;

    private RentAccountBO rentAccountBO;
    // 任务的到期时间
    private long expireTime;

    // 返回距离任务到期还有多长时间
    @Override
    public long getDelay(TimeUnit unit) {
        return unit.convert(expireTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    // 对 Delayed 接口中方法的实现，用于按照到期时间排序
    @Override
    public int compareTo(Delayed other) {
        if (other == this) {
            return 0;
        }
        if (other instanceof AutoReturnTask) {
            AutoReturnTask otherTask = (AutoReturnTask) other;
            long diff = expireTime - otherTask.expireTime;
            return diff > 0 ? 1 : (diff == 0 ? 0 : -1);
        }
        long d = (getDelay(TimeUnit.MILLISECONDS) - other.getDelay(TimeUnit.MILLISECONDS));
        return d > 0 ? 1 : (d == 0 ? 0 : -1);
    }

    // 具体任务的执行逻辑
    public void execute() {
        rentAccountBizService.autoReturnAccount(ReturnAccountRequest.newBuilder()
                .setUserId(rentAccountBO.getUserId())
                .setBorrower("autoreturn")
                .build());
    }
}