package com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo;

import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.ApprovalFlowTypeEnum;

import lombok.Data;

/**
 * @version 1.0
 * <AUTHOR>
 * @Date 2024/11/8 17:25
 * @注释 发起审批流参数
 */
@Data
public class DPMApprovalParam {

    /**
     * 审批流类型
     */
    private ApprovalFlowTypeEnum flowType;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 标题
     */
    private String title;

    /**
     * bizCode，每种审批流通过该参数获取核心领域模型
     */
    private String bizCode;

    /**
     * 审批页面表单数据
     */
    private Object businessSummary;
    /**
     * 账号id
     */
    private String userId;

    private String duration;

    private String account;

    private Integer loginType;

    private String password;
}
