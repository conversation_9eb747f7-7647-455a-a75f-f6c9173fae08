package com.kuaishou.kwaishop.qa.risk.center.db.mapper.stress;

import static com.kuaishou.kwaishop.qa.risk.center.config.constants.DataSourceConstants.RISK_CENTER_SOURCE_NAME;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.infra.boot.jdbc.datasource.DataSourceRouting;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceBaseDO;

@Mapper
@DataSourceRouting(RISK_CENTER_SOURCE_NAME)
public interface StressServiceBaseMapper extends BaseMapper<StressServiceBaseDO> {
    @Select({"<script>"
            + "SELECT * FROM stress_service_base "
            + " WHERE 1=1 "
            + "<if test='serviceId > 0'>and service_id = #{serviceId}</if>"
            + "<if test='serviceName != null'>and service_name like CONCAT('%',#{serviceName},'%')</if>"
            + "ORDER BY create_time DESC LIMIT #{startNo},#{pageSize} "
            + "</script>"})
    List<StressServiceBaseDO> queryStressServiceLists(@Param("serviceId") Long serviceId, @Param("serviceName") String serviceName,
            @Param("startNo") Integer startNo, @Param("pageSize")Integer pageSize);

    @Select({"<script>"
            + "SELECT count(id) FROM stress_service_base "
            + " WHERE 1=1 "
            + "<if test='serviceId > 0'>and service_id = #{serviceId}</if>"
            + "<if test='serviceName != null'> and service_name like CONCAT('%',#{serviceName},'%')</if>"
            + "</script>"})
    Long countStressServiceList(@Param("serviceId") Long serviceId, @Param("serviceName") String serviceName);


    @Select({"<script>"
            + "SELECT * FROM stress_service_base "
            + " WHERE "
            + "service_id = #{serviceId} limit 0,1"
            + "</script>"})
    StressServiceBaseDO queryStressServiceBaseByServiceId(@Param("serviceId") Long serviceId);
}
