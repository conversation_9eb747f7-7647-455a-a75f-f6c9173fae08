package com.kuaishou.kwaishop.qa.risk.center.domain.stress.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StressServiceBO {
    /**
     * 服务名
     */
    private String serviceName;

    /**
     * 服务描述
     */
    private String serviceDesc;

    private String serviceGroup;

    /**
     * 场景状态: 0-创建完成, 1-删除
     */
    private Integer status;

    private Long createTime;

    private Long updateTime;

    private Integer deleted;
}
