package com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.impl;

import org.springframework.stereotype.Component;

import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.entity.EntityDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertAbstract;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.convert.EntityConvertHandler;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.EntityQueryBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.bo.TeamEntityBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.entity.model.enums.EntityTypeEnum;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.CreateEntityRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.QueryEntityPageListRequest;
import com.kuaishou.kwaishop.qa.risk.center.protobuf.entity.UpdateEntityRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-11-10
 */
@Component
public class TeamEntityConvertHandlerImpl extends EntityConvertAbstract implements EntityConvertHandler {

    @Override
    public EntityDO buildCreateDO(EntityBO entityBO) {
        TeamEntityBO teamEntityBO = (TeamEntityBO) entityBO;
        return EntityDO.builder()
                .entityType(getEntityType())
                .entityId(teamEntityBO.getEntityId())
                .name(teamEntityBO.getName())
                .status(teamEntityBO.getStatus())
                .creator(teamEntityBO.getOperator())
                .modifier(teamEntityBO.getOperator())
                .extra1(teamEntityBO.getLeader())
                .build();
    }

    @Override
    public EntityDO buildModifyDO(EntityDO existDO, EntityBO entityBO) {
        TeamEntityBO teamEntityBO = (TeamEntityBO) entityBO;
        existDO.setName(teamEntityBO.getName());
        existDO.setExtra1(teamEntityBO.getLeader());
        existDO.setModifier(teamEntityBO.getOperator());
        existDO.setStatus(teamEntityBO.getStatus());
        return existDO;
    }

    @Override
    public Integer getEntityType() {
        return EntityTypeEnum.TEAM_TYPE.getCode();
    }

    @Override
    public EntityBO buildCreateBO(CreateEntityRequest request) {
        return TeamEntityBO.builder()
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .name(request.getName())
                .leader(request.getExtra1())
                .operator(request.getOperator())
                .build();
    }

    @Override
    public EntityBO buildModifyBO(UpdateEntityRequest request) {
        return TeamEntityBO.builder()
                .id(request.getId())
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .name(request.getName())
                .leader(request.getExtra1())
                .operator(request.getOperator())
                .build();
    }

    @Override
    public EntityQueryBO buildQueryListBO(QueryEntityListRequest request) {
        return EntityQueryBO.builder()
                .id(request.getId())
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .nameEq(request.getName())
                .leader(request.getExtra1())
                .build();
    }

    @Override
    public EntityQueryBO buildQueryPageListBO(QueryEntityPageListRequest request) {
        return EntityQueryBO.builder()
                .id(request.getId())
                .entityId(request.getEntityId())
                .entityType(request.getEntityType())
                .nameEq(request.getName())
                .leader(request.getExtra1())
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .build();
    }


}
