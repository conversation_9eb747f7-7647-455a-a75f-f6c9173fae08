package com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.impl;

import static com.kuaishou.kwaishop.qa.risk.center.config.exception.BizErrorCode.QUERY_SCENARIO_ERROR;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.kuaishou.kwaishop.qa.risk.center.common.exception.BizException;
import com.kuaishou.kwaishop.qa.risk.center.db.bo.PageBO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressInterfaceRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressScenarioRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.dao.stress.StressServiceRecordDAO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressInterfaceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressScenarioRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.db.domainobject.stress.StressServiceRecordDO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressInterfaceRecordBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressScenarioRecordBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.model.QueryStressServiceRecordBO;
import com.kuaishou.kwaishop.qa.risk.center.domain.stress.service.StressScenarioRecordService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class StressScenarioRecordServiceImpl implements StressScenarioRecordService {
    @Resource
    private StressScenarioRecordDAO stressScenarioRecordDAO;

    @Resource
    private StressServiceRecordDAO stressServiceRecordDAO;

    @Resource
    private StressInterfaceRecordDAO stressInterfaceRecordDAO;

    @Override
    public PageBO<StressScenarioRecordDO> queryPageList(QueryStressScenarioRecordBO queryBO) {
        if (queryBO == null) {
            throw new BizException(QUERY_SCENARIO_ERROR);
        }
        return stressScenarioRecordDAO.queryPageList(queryBO);
    }

    @Override
    public StressScenarioRecordDO getScenarioRecordWithInterface(Long id) {
        StressScenarioRecordDO scenarioRecordDO = stressScenarioRecordDAO.getById(id);
        scenarioRecordDO.setServiceRecords(new ArrayList<>());
        scenarioRecordDO.setInterfaceRecords(new ArrayList<>());
        if (scenarioRecordDO != null) {
            QueryStressServiceRecordBO queryBO = new QueryStressServiceRecordBO();
            queryBO.setScenarioRecordId(scenarioRecordDO.getId());
            List<StressServiceRecordDO> list = stressServiceRecordDAO.queryStressSerRecords(queryBO);
            scenarioRecordDO.setServiceRecords(list);

            QueryStressInterfaceRecordBO.builder().build();
            List<StressInterfaceRecordDO> interfaceRecords = stressInterfaceRecordDAO.queryStressInterfaceRecords(
                    QueryStressInterfaceRecordBO.builder().scenarioRecordId(scenarioRecordDO.getId()).build());
            scenarioRecordDO.setInterfaceRecords(interfaceRecords);
        }


        return scenarioRecordDO;
    }

}
